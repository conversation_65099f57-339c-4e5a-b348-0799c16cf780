package model

import (
	"ros-api-go/pkg/util"
	"time"
)

// ComboInfo 套餐详细信息
type ComboInfo struct {
	ID         int64             `json:"id"`           // 套餐detail编号
	FoodID     int64             `json:"food_id"`      // 美食编号
	SpecFoodID int64             `json:"spec_food_id"` // 规格ID（如果没有规格，则与FoodID相同）
	Count      float64           `json:"count"`        // 美食数量
	Remarks    string            `json:"remarks"`      // 备注
	Additions  []ComboItemInfo   `json:"additions"`    // 加料信息
	Methods    []ComboMethodInfo `json:"methods"`      // 做法信息
	LunchBoxes []ComboItemInfo   `json:"lunch_boxes"`  // 餐盒信息
}

// ComboItemInfo 套餐子项信息（用于加料和餐盒）
type ComboItemInfo struct {
	FoodID     int64   `json:"food_id"`      // 美食ID
	SpecFoodID int64   `json:"spec_food_id"` // 规格ID
	Count      float64 `json:"count"`        // 数量
	Remarks    string  `json:"remarks"`      // 备注
}

// ComboMethodInfo 套餐做法信息
type ComboMethodInfo struct {
	MethodID int64 `json:"method_id"` // 做法ID
	GroupID  int64 `json:"group_id"`  // 分组ID
	Count    int   `json:"count"`     // 数量
}

const (
	OrderDetailTypeFood     = 1 // 美食
	OrderDetailTypeAddition = 2 // 加料
	OrderDetailTypeLunchBox = 3 // 饭盒
	OrderDetailTypeMethod   = 4 // 做法
)

// OrderDetailModel 订单详情
type OrderDetailModel struct {
	ID            int64                   `json:"id" gorm:"column:id" gorm:"size:20;primaryKey;"`     // ID
	MerchantNo    string                  `json:"merchant_no" gorm:"column:merchant_no"`              // 商户编号
	OrderNo       string                  `json:"order_no" gorm:"column:order_no"`                    // 订单编号
	OrderID       int64                   `json:"order_id" gorm:"column:order_id"`                    // 订单ID
	Pid           int64                   `json:"pid" gorm:"column:pid"`                              // 父级详情ID
	FoodID        int64                   `json:"food_id" gorm:"column:food_id"`                      // 美食编号
	SpecFoodID    int64                   `json:"spec_food_id" gorm:"column:spec_food_id"`            // 规格美食编号
	MethodID      int64                   `json:"method_id" gorm:"column:method_id"`                  // 做法编号
	Type          int64                   `json:"type" gorm:"column:type"`                            // 美食类型: 1 美食, 2 加料, 3 饭盒, 4 做法
	FoodsCount    float64                 `json:"foods_count" gorm:"column:foods_count"`              // 美食数量
	CostPrice     float64                 `json:"cost_price" gorm:"column:cost_price"`                // 成本价
	OriginalPrice float64                 `json:"original_price" gorm:"column:original_price"`        // 原价
	VipPrice      float64                 `json:"vip_price" gorm:"column:vip_price"`                  // 会员价
	Price         float64                 `json:"price" gorm:"column:price"`                          // 出售价
	TotalPrice    float64                 `json:"total_price" gorm:"column:total_price"`              // 总价格
	IsPrint       bool                    `json:"is_print" gorm:"column:is_print"`                    // 是否打印
	UserID        int64                   `json:"user_id" gorm:"column:user_id"`                      // 点菜人编号
	State         int                     `json:"state" gorm:"column:state"`                          // 订单状态 - 1 新订单（人数确定，未点菜）、2 未支付订单（已点菜，订单已提交）、3 已结账订单（已结账）、4 取消订单（退菜）
	Remarks       *string                 `json:"remarks" gorm:"column:remarks"`                      // 备注
	IsCombo       bool                    `json:"is_combo" gorm:"column:is_combo"`                    // 是否是套餐
	ComboInfo     *util.TArray[ComboInfo] `json:"combo_info" gorm:"column:combo_info"`                // 套餐详细信息
	IsSync        bool                    `json:"is_sync" gorm:"column:is_sync"`                      // 是否已同步
	CreatedAt     *time.Time              `json:"created_at" gorm:"column:created_at"`                // 创建时间
	UpdatedAt     *time.Time              `json:"updated_at" gorm:"column:updated_at"`                // 更新时间
	DeletedAt     *time.Time              `json:"deleted_at" gorm:"column:deleted_at"`                // 删除时间
	Order         *OrderModel             `json:"order" gorm:"foreignKey:ID;references:OrderID"`      // 订单
	Food          *FoodModel              `json:"food" gorm:"foreignKey:FoodID;references:ID"`        // 美食
	Creator       *MerchantEmployeeModel  `json:"creator" gorm:"foreignKey:UserID;references:UserID"` // 点菜人
}

func (model *OrderDetailModel) TableName() string {
	return "order_details"
}

// OrderDetailStatisticsModel 订单详情统计
type OrderDetailStatisticsModel struct {
	FoodID         int64 `gorm:"column:food_id"`
	Count          float64
	RealAmount     float64
	CostAmount     float64
	DiscountAmount float64
	DipositAmount  float64
	Food           FoodBasicModel `gorm:"foreignKey:FoodID"`
}

func (model *OrderDetailStatisticsModel) TableName() string {
	return "order_details"
}

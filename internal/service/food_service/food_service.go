package food_service

import (
	"context"
	"github.com/samber/lo"
	"ros-api-go/internal/consts"
	"ros-api-go/internal/http/request/food_request"
	"ros-api-go/internal/model"
	"ros-api-go/pkg/cachex"
	"ros-api-go/pkg/encoding/json"
	"ros-api-go/pkg/errors"
	"ros-api-go/pkg/util"
	"time"

	"gorm.io/gorm"
)

// FoodsService 美食业务逻辑
type FoodsService struct {
	DB    *gorm.DB
	Cache cachex.Cacher
	Trans *util.Trans
}

// GetMerchantAvailableFoods 获取商户可用的菜品业务逻辑(用于线上模式web和点菜端显示菜品列表)
func (serv *FoodsService) GetMerchantAvailableFoods(ctx context.Context, merchantNo string, catId int64, keyword string, sellClearAll string) ([]*model.FoodModel, error) {
	var foods []*model.FoodModel
	// 预加载与食品相关的组合信息，仅加载类型为1的组合，并预加载其子组合（类型为2）
	query := util.GetDB(ctx, serv.DB).
		Select("foods.*").
		Where("foods.pid = ? AND foods.merchant_no = ? AND foods.state = ?", 0, merchantNo, model.FoodStateNormal).
		Preload("Specs").
		Preload("Combos", func(db *gorm.DB) *gorm.DB {
			return db.Where("type = ?", 1).
				Preload("Childs", "type = ?", 2)
		})

	// 应用过滤器
	serv.applyFilters(query, merchantNo, catId, keyword, sellClearAll)

	// 返回食品列表和可能的错误信息
	return foods, query.Find(&foods).Error
}

// GetMerchantAvailableFoodsV2 获取商户可用的菜品业务逻辑(用于线上模式web和点菜端显示菜品列表)
func (serv *FoodsService) GetMerchantAvailableFoodsV2(ctx context.Context, merchantNo string, catId int64, keyword string, sellClearAll string) ([]*model.FoodModel, error) {
	var foods []*model.FoodModel
	// 预加载与食品相关的组合信息，仅加载类型为1的组合，并预加载其子组合（类型为2）
	query := util.GetDB(ctx, serv.DB).
		Select("foods.*").
		Where("foods.pid = ? AND foods.merchant_no = ? AND foods.state = ?", 0, merchantNo, model.FoodStateNormal).
		Preload("Specs", func(db *gorm.DB) *gorm.DB {
			return db.Preload("FoodLunchBoxes.LunchBox")
		}).
		Preload("FoodLunchBoxes.LunchBox").
		Preload("FoodAdditions", func(db *gorm.DB) *gorm.DB {
			return db.Preload("Addition").Preload("Category")
		}).
		Preload("FoodConfigs").
		Preload("FoodMethods", func(db *gorm.DB) *gorm.DB {
			return db.Preload("Method").Preload("Group")
		}).
		Preload("Combos", func(db *gorm.DB) *gorm.DB {
			return db.Where("type = ?", 1).
				Preload("Childs", "type = ?", 2)
		})

	// 应用过滤器
	serv.applyFilters(query, merchantNo, catId, keyword, sellClearAll)

	// 返回食品列表和可能的错误信息
	return foods, query.Find(&foods).Error
}

// applyFilters 应用过滤器
func (serv *FoodsService) applyFilters(query *gorm.DB, merchantNo string, catId int64, keyword string, sellClearAll string) {
	// 筛选已禁用的分类
	query = query.Joins("JOIN food_categories ON foods.food_category_id = food_categories.id").
		Where("food_categories.merchant_no = ? AND food_categories.state = ?", merchantNo, model.FoodCategoryStateNormal)

	// 分类
	if catId != 0 {
		query = query.Where("food_category_id = ?", catId)
	}
	// 关键字搜索
	if keyword != "" {
		query = query.Where("foods.name_ug LIKE ? OR foods.name_zh LIKE ?", "%"+keyword+"%", "%"+keyword+"%")
	}

	// 如果 sellClearAll 参数存在，则仅筛选出清理状态为1的食品
	if sellClearAll != "" {
		query = query.Where("foods.cell_clear_state = ?", 1)
	}

	// 按照排序字段降序排列
	query = query.Order("sort asc,type asc")
}

// GetParentFoodsForAssociation 获取父级美食列表（用于规格/加料关联）
func (serv *FoodsService) GetParentFoodsForAssociation(ctx context.Context, merchantNo string, catId int64) ([]*model.FoodModel, error) {
	var foods []*model.FoodModel
	query := util.GetDB(ctx, serv.DB).
		Select("foods.*").
		Preload("FoodCategory").
		Where("foods.merchant_no = ? AND foods.state = ? AND foods.type = ?", merchantNo, model.FoodStateNormal, model.FoodTypeFood).
		Where("foods.pid = 0 AND foods.is_combo = false AND foods.deleted_at IS NULL")

	// 筛选已禁用的分类
	query = query.Joins("JOIN food_categories ON foods.food_category_id = food_categories.id").
		Where("food_categories.merchant_no = ? AND food_categories.state = ?", merchantNo, model.FoodCategoryStateNormal)

	// 分类
	if catId != 0 {
		query = query.Where("food_category_id = ?", catId)
	}

	return foods, query.Find(&foods).Error
}

// GetSpecFoodsForAssociation 获取规格关联的美食列表（用于餐盒关联）
func (serv *FoodsService) GetSpecFoodsForAssociation(ctx context.Context, merchantNo string, catId int64) ([]*model.FoodModel, error) {
	var foods []*model.FoodModel
	query := util.GetDB(ctx, serv.DB).
		Select("foods.*").
		Preload("FoodCategory").
		Preload("Specs").
		Where("foods.merchant_no = ? AND foods.state = ? AND foods.type = ?", merchantNo, model.FoodStateNormal, model.FoodTypeFood).
		Where("foods.pid = 0 AND foods.is_combo = false AND foods.deleted_at IS NULL")

	// 筛选已禁用的分类
	query = query.Joins("JOIN food_categories ON foods.food_category_id = food_categories.id").
		Where("food_categories.merchant_no = ? AND food_categories.state = ?", merchantNo, model.FoodCategoryStateNormal)

	// 分类
	if catId != 0 {
		query = query.Where("food_category_id = ?", catId)
	}

	return foods, query.Find(&foods).Error
}

// GetFoodsList 获取美食列表
func (serv *FoodsService) GetFoodsList(ctx context.Context, req *food_request.FoodListRequest) ([]*model.FoodModel, error) {
	var list []*model.FoodModel

	db := util.GetDB(ctx, serv.DB).
		Model(&model.FoodModel{}).
		Order("foods.created_at asc").
		Order("foods.food_category_id asc").
		Preload("FoodCategory").
		Select("foods.*, (SELECT COUNT(*) FROM foods as foods2 WHERE foods2.pid = foods.id AND foods.deleted_at IS NULL) as specs_count")

	if req.MerchantNo != "" {
		db = db.Where("foods.merchant_no = ?", req.MerchantNo)
	}

	if req.FoodCategoryID > 0 {
		db = db.Where("foods.food_category_id = ?", req.FoodCategoryID)
	}

	if req.Keyword != "" {
		db = db.Where("foods.name_ug LIKE ? OR foods.name_zh LIKE ? OR foods.shortcut_code LIKE ?",
			"%"+req.Keyword+"%", "%"+req.Keyword+"%", "%"+req.Keyword+"%")
	}

	if req.State != nil {
		db = db.Where("foods.state = ?", *req.State)
	}

	if req.SellClearAll == "1" {
		db = db.Where("foods.cell_clear_state = ?", 1)
	}

	if req.SupportScanOrder != nil {
		db = db.Where("foods.support_scan_order = ?", *req.SupportScanOrder)
	}

	err := db.Find(&list).Error
	if err != nil {
		return nil, err
	}

	return list, nil
}

// GetFoodDetail 获取美食详情
func (serv *FoodsService) GetFoodDetail(ctx context.Context, foodId int64, merchantNo string) (*model.FoodModel, error) {
	var food model.FoodModel
	one, err := util.FindOne(ctx, util.GetDB(ctx, serv.DB).
		Preload("Specs", func(db *gorm.DB) *gorm.DB {
			return db.Preload("FoodLunchBoxes.LunchBox")
		}).
		Preload("FoodLunchBoxes.LunchBox").
		Preload("FoodAdditions", func(db *gorm.DB) *gorm.DB {
			return db.Preload("Addition").Preload("Category")
		}).
		Preload("FoodConfigs").
		Preload("FoodMethods", func(db *gorm.DB) *gorm.DB {
			return db.Preload("Method").Preload("Group")
		}).
		Preload("Combos", func(db *gorm.DB) *gorm.DB {
			return db.Where("type = ?", 1).
				Preload("Childs", "type = ?", 2)
		}).
		Where("id = ? AND merchant_no = ? AND deleted_at IS NULL", foodId, merchantNo), &food)
	if err != nil {
		return nil, err
	}
	if !one {
		return nil, errors.NotFound("", "FoodNotFound")
	}

	return &food, nil
}

// CreateFood 创建美食
func (serv *FoodsService) CreateFood(ctx context.Context, req *food_request.FoodRequest, merchantNo string) (*model.FoodModel, error) {

	err := serv.validateFood(ctx, req, merchantNo, 0)
	if err != nil {
		return nil, err
	}
	// 创建美食记录
	food := &model.FoodModel{
		MerchantNo:       merchantNo,
		FoodCategoryID:   req.FoodCategoryID,
		Image:            req.Image,
		ShortcutCode:     req.ShortcutCode,
		NameUg:           req.NameUg,
		NameZh:           req.NameZh,
		CostPrice:        req.CostPrice,
		VipPrice:         req.VipPrice,
		Price:            req.Price,
		FormatID:         req.FormatID,
		IsSpecialFood:    req.IsSpecialFood,
		SupportScanOrder: req.SupportScanOrder,
		CellClearState:   req.CellClearState,
		SellClearCount:   req.SellClearCount,
		RemainingCount:   req.RemainingCount,
		IsCombo:          false,
		Sort:             req.Sort,
		State:            req.State,
		IsSync:           false,
		CreatedAt:        time.Now(),
		UpdatedAt:        time.Now(),
	}

	err = serv.Trans.Exec(ctx, func(ctx context.Context) error {
		// 创建美食
		err := util.GetDB(ctx, serv.DB).Create(food).Error
		if err != nil {
			return err
		}
		// 处理规格信息
		err = serv.saveFoodSpecs(ctx, food, req, merchantNo)
		if err != nil {
			return err
		}
		// 创建做法/加料/餐盒
		err = serv.saveFoodRelations(ctx, food.ID, req, merchantNo)
		if err != nil {
			return err
		}
		// 创建配置信息
		err = serv.saveFoodConfigs(ctx, food.ID, req, merchantNo)
		if err != nil {
			return err
		}
		return nil
	})

	return food, err
}

// UpdateFood 更新美食
func (serv *FoodsService) UpdateFood(ctx context.Context, foodId int64, req *food_request.FoodRequest, merchantNo string) (*model.FoodModel, error) {

	// 检查美食是否存在且属于该商户
	var food *model.FoodModel
	one, err := util.FindOne(ctx, util.GetDB(ctx, serv.DB).
		Where("id = ? AND merchant_no = ?", foodId, merchantNo), &food)
	if err != nil {
		return nil, err
	}
	if !one {
		return nil, errors.NotFound("", "FoodNotFound")
	}

	err = serv.validateFood(ctx, req, merchantNo, foodId)
	if err != nil {
		return nil, err
	}

	// 更新美食记录
	food.FoodCategoryID = req.FoodCategoryID
	food.Image = req.Image
	food.ShortcutCode = req.ShortcutCode
	food.NameUg = req.NameUg
	food.NameZh = req.NameZh
	food.CostPrice = req.CostPrice
	food.VipPrice = req.VipPrice
	food.Price = req.Price
	food.FormatID = req.FormatID
	food.IsSpecialFood = req.IsSpecialFood
	food.SupportScanOrder = req.SupportScanOrder
	food.CellClearState = req.CellClearState
	food.SellClearCount = req.SellClearCount
	food.RemainingCount = req.RemainingCount
	food.IsCombo = req.IsCombo
	food.Sort = req.Sort
	food.State = req.State
	food.UpdatedAt = time.Now()

	err = serv.Trans.Exec(ctx, func(ctx context.Context) error {
		err = util.GetDB(ctx, serv.DB).Save(&food).Error
		if err != nil {
			return err
		}
		// 处理规格信息
		err = serv.saveFoodSpecs(ctx, food, req, merchantNo)
		if err != nil {
			return err
		}
		// 创建做法/加料/餐盒
		err = serv.saveFoodRelations(ctx, food.ID, req, merchantNo)
		if err != nil {
			return err
		}
		// 创建配置信息
		err = serv.saveFoodConfigs(ctx, food.ID, req, merchantNo)
		if err != nil {
			return err
		}
		return nil
	})

	return food, nil
}

// validateFood 验证美食数据
func (serv *FoodsService) validateFood(ctx context.Context, req *food_request.FoodRequest, merchantNo string, foodId int64) error {
	// 检查分类是否存在且属于该商户
	var categoryCount int64
	err := util.GetDB(ctx, serv.DB).Model(&model.FoodCategoryModel{}).
		Where("id = ? AND merchant_no = ? AND state = ?", req.FoodCategoryID, merchantNo, 1).
		Count(&categoryCount).Error
	if err != nil {
		return err
	}
	if categoryCount == 0 {
		return errors.BadRequest("", "FoodCategoryNotFound")
	}

	// 检查名称是否已存在
	var nameCount int64
	query := util.GetDB(ctx, serv.DB).Model(&model.FoodModel{}).
		Where("merchant_no = ? AND (name_ug = ? OR name_zh = ?)", merchantNo, req.NameUg, req.NameZh)

	if foodId > 0 {
		query = query.Where("id <> ?", foodId)
	}

	err = query.Count(&nameCount).Error
	if err != nil {
		return err
	}
	if nameCount > 0 {
		return errors.BadRequest("", "FoodNameExists")
	}

	// 检查规格数据是否有效
	if req.Specs != nil && len(req.Specs) > 0 {
		specIds := make([]int64, 0)
		lunchBoxIds := make([]int64, 0)
		for _, spec := range req.Specs {
			specIds = append(specIds, spec.SpecId)
			if spec.LunchBoxes != nil && len(spec.LunchBoxes) > 0 {
				for _, box := range spec.LunchBoxes {
					// 如果不存在,则添加到数组中
					_, exists := lo.Find(lunchBoxIds, func(id int64) bool {
						return id == box.LunchBoxID
					})
					if !exists {
						lunchBoxIds = append(lunchBoxIds, box.LunchBoxID)
					}
				}
			}
		}
		var specCount int64
		err = util.GetDB(ctx, serv.DB).Model(&model.FoodSpecModel{}).
			Where("merchant_no = ? AND id IN (?) AND state = ?", merchantNo, specIds, model.FoodSpecStateNormal).
			Count(&specCount).Error
		if err != nil {
			return err
		}
		if specCount != int64(len(req.Specs)) {
			return errors.BadRequest("", "FoodSpecNotFound")
		}

		// 检查餐盒是否存在
		var lunchBoxCount int64
		err = util.GetDB(ctx, serv.DB).Model(&model.FoodModel{}).
			Where("merchant_no = ? AND id IN (?) AND type = ? AND state = ?", merchantNo, lunchBoxIds, model.FoodTypeLunchBox, model.FoodStateNormal).
			Count(&lunchBoxCount).Error
		if err != nil {
			return err
		}
		if lunchBoxCount != int64(len(lunchBoxIds)) {
			return errors.BadRequest("", "LunchBoxNotFound")
		}
	} else {
		// 如果没有规格, 则检查商品价格是否有效
		if req.Price <= 0 {
			return errors.BadRequest("", "FoodPriceInvalid")
		}
		if req.VipPrice <= 0 {
			return errors.BadRequest("", "FoodVipPriceInvalid")
		}
		if req.CostPrice <= 0 {
			return errors.BadRequest("", "FoodCostPriceInvalid")
		}
	}

	// 检查做法是否存在
	if req.MethodIds != nil && len(req.MethodIds) > 0 {
		// 检查做法是否存在
		var methodCount int64
		err = util.GetDB(ctx, serv.DB).Model(&model.MethodModel{}).
			Where("merchant_no = ? AND id IN (?) AND state = ?", merchantNo, req.MethodIds, model.MethodStateNormal).
			Count(&methodCount).Error
		if err != nil {
			return err
		}
		if methodCount != int64(len(req.MethodIds)) {
			return errors.BadRequest("", "MethodNotFound")
		}
	}

	// 检查加料是否存在
	if req.AdditionIds != nil && len(req.AdditionIds) > 0 {
		// 检查加料是否存在
		var additionCount int64
		err = util.GetDB(ctx, serv.DB).Model(&model.FoodModel{}).
			Where("merchant_no = ? AND id IN (?) AND type = ? AND state = ?", merchantNo, req.AdditionIds, model.FoodTypeAddition, model.FoodStateNormal).
			Count(&additionCount).Error
		if err != nil {
			return err
		}
		if additionCount != int64(len(req.AdditionIds)) {
			return errors.BadRequest("", "AdditionNotFound")
		}
	}

	// 检查做法配置是否有效
	if req.MethodConfigs != nil && len(req.MethodConfigs) > 0 {
		var methodCount int64
		groupIds := make([]int64, 0)
		for _, config := range req.MethodConfigs {
			groupIds = append(groupIds, config.GroupID)
		}
		err = util.GetDB(ctx, serv.DB).Model(&model.MethodGroupModel{}).
			Where("merchant_no = ? AND id IN (?) AND state = ?", merchantNo, groupIds, model.MethodStateNormal).
			Count(&methodCount).Error
		if err != nil {
			return err
		}
		if methodCount != int64(len(groupIds)) {
			return errors.BadRequest("", "MethodGroupNotFound")
		}
	}

	// 检查加料配置是否有效
	if req.AdditionConfigs != nil && len(req.AdditionConfigs) > 0 {
		var additionCount int64
		categoryIds := make([]int64, 0)
		for _, config := range req.AdditionConfigs {
			categoryIds = append(categoryIds, config.GroupID)
		}
		err = util.GetDB(ctx, serv.DB).Model(&model.FoodCategoryModel{}).
			Where("merchant_no = ? AND id IN (?) AND state = ? AND deleted_at IS NULL", merchantNo, categoryIds, model.FoodCategoryStateNormal).
			Count(&additionCount).Error
		if err != nil {
			return err
		}
		if additionCount != int64(len(categoryIds)) {
			return errors.BadRequest("", "AdditionCategoryNotFound")
		}
	}

	return nil
}

// saveFoodSpecs 保存规格数据
func (serv *FoodsService) saveFoodSpecs(ctx context.Context, parentFood *model.FoodModel, req *food_request.FoodRequest, merchantNo string) error {
	// 批量查询已存在的规格美食记录
	var existingSpecFoods []model.FoodModel
	specIds := make([]int64, len(req.Specs))
	for i, spec := range req.Specs {
		specIds[i] = spec.SpecId
	}
	err := util.GetDB(ctx, serv.DB).
		Where("merchant_no = ? AND pid = ? AND spec_id IN	(?)",
			merchantNo, parentFood.ID, specIds).
		Find(&existingSpecFoods).Error
	if err != nil {
		return err
	}

	// 创建已存在规格美食的映射
	existingSpecFoodMap := make(map[int64]*model.FoodModel)
	for i := range existingSpecFoods {
		existingSpecFood := &existingSpecFoods[i]
		existingSpecFoodMap[existingSpecFood.SpecID] = existingSpecFood
	}

	// 查询规格数据
	var specsFromDB []model.FoodSpecModel
	err = util.GetDB(ctx, serv.DB).
		Where(" merchant_no = ? AND id IN	(?) AND state > ?",
			merchantNo, specIds, model.FoodSpecStateDeleted).
		Find(&specsFromDB).Error
	if err != nil {
		return err
	}
	// 创建已存在规格美食的映射
	specsFromDbMap := make(map[int64]*model.FoodSpecModel)
	for i := range specsFromDB {
		spec := &specsFromDB[i]
		specsFromDbMap[spec.ID] = spec
	}
	now := time.Now()

	for _, specItem := range req.Specs {
		specFromDB := specsFromDbMap[specItem.SpecId]
		var specFood *model.FoodModel
		var exists bool
		if specFood, exists = existingSpecFoodMap[specItem.SpecId]; exists {
			// 更新现有记录
			specFood.NameZh = specFromDB.NameZh
			specFood.NameUg = specFromDB.NameUg
			specFood.Price = specItem.Price
			specFood.VipPrice = specItem.VipPrice
			specFood.CostPrice = specItem.CostPrice
			specFood.UpdatedAt = now
			err = util.GetDB(ctx, serv.DB).Where("id = ?", specFood.ID).Updates(specFood).Error
			if err != nil {
				return err
			}
		} else {
			// 创建新记录
			specFood = &model.FoodModel{
				MerchantNo: merchantNo,
				Pid:        parentFood.ID,
				SpecID:     specItem.SpecId,
				Type:       model.FoodTypeFood,
				NameZh:     specFromDB.NameZh,
				NameUg:     specFromDB.NameUg,
				CostPrice:  specItem.CostPrice,
				Price:      specItem.Price,
				VipPrice:   specItem.VipPrice,
				// 继承原菜品的其他属性
				FoodCategoryID:   parentFood.FoodCategoryID,
				Image:            parentFood.Image,
				FormatID:         parentFood.FormatID,
				IsSpecialFood:    parentFood.IsSpecialFood,
				SupportScanOrder: parentFood.SupportScanOrder,
				IsCombo:          false,
				Sort:             parentFood.Sort,
				State:            parentFood.State,
				IsSync:           false,
				CreatedAt:        now,
				UpdatedAt:        now,
			}
			err = util.GetDB(ctx, serv.DB).Create(&specFood).Error
			if err != nil {
				return err
			}
		}

		// 保存餐盒关系
		if specItem.LunchBoxes != nil && len(specItem.LunchBoxes) > 0 {
			// 删除现有餐盒关系
			err = util.GetDB(ctx, serv.DB).
				Where("food_id = ? AND merchant_no = ?", specFood.ID, merchantNo).
				Delete(&model.FoodLunchBoxModel{}).Error
			if err != nil {
				return err
			}
			// 创建新的餐盒关系
			foodLunchBoxes := make([]*model.FoodLunchBoxModel, 0)
			for _, box := range specItem.LunchBoxes {
				foodLunchBoxes = append(foodLunchBoxes, &model.FoodLunchBoxModel{
					MerchantNo: merchantNo,
					FoodID:     specFood.ID,
					LunchBoxID: box.LunchBoxID,
					Count:      box.Count,
				})
			}
			err = util.GetDB(ctx, serv.DB).Create(foodLunchBoxes).Error
			if err != nil {
				return err
			}
		}
	}

	return nil
}

// saveFoodRelations 保存做法/加料关系
func (serv *FoodsService) saveFoodRelations(ctx context.Context, foodId int64, req *food_request.FoodRequest, merchantNo string) error {
	// 删除现有关联关系
	err := util.GetDB(ctx, serv.DB).
		Where("food_id = ? AND merchant_no = ?", foodId, merchantNo).
		Delete(&model.FoodMethodModel{}).Error
	if err != nil {
		return err
	}

	// 保存新的关联关系
	if req.MethodIds != nil && len(req.MethodIds) > 0 {
		var methods []*model.MethodModel
		err = util.GetDB(ctx, serv.DB).Where("merchant_no = ? AND id IN (?)", merchantNo, req.MethodIds).
			Find(&methods).Error
		if err != nil {
			return err
		}
		foodMethods := make([]*model.FoodMethodModel, 0, len(methods))
		for _, method := range methods {
			foodMethods = append(foodMethods, &model.FoodMethodModel{
				FoodID:     foodId,
				GroupID:    method.GroupID,
				MethodID:   method.ID,
				MerchantNo: merchantNo,
			})
		}
		err = util.GetDB(ctx, serv.DB).Save(foodMethods).Error
		if err != nil {
			return err
		}
	}

	// 删除现有加料关系
	err = util.GetDB(ctx, serv.DB).
		Where("food_id = ? AND merchant_no = ?", foodId, merchantNo).
		Delete(&model.FoodAdditionModel{}).Error
	if err != nil {
		return err
	}

	// 保存新的加料关系
	if req.AdditionIds != nil && len(req.AdditionIds) > 0 {
		var additions []*model.FoodModel
		err = util.GetDB(ctx, serv.DB).Where("merchant_no = ? AND id IN (?)", merchantNo, req.AdditionIds).
			Find(&additions).Error
		if err != nil {
			return err
		}
		foodAdditions := make([]*model.FoodAdditionModel, 0, len(additions))
		for _, addition := range additions {
			foodAdditions = append(foodAdditions, &model.FoodAdditionModel{
				FoodID:     foodId,
				CategoryID: addition.FoodCategoryID,
				AdditionID: addition.ID,
				MerchantNo: merchantNo,
			})
		}
		err = util.GetDB(ctx, serv.DB).Create(foodAdditions).Error
		if err != nil {
			return err
		}
	}

	return nil
}

// saveFoodConfigs 保存食品配置
func (serv *FoodsService) saveFoodConfigs(ctx context.Context, foodId int64, req *food_request.FoodRequest, merchantNo string) error {
	// 删除现有配置
	err := util.GetDB(ctx, serv.DB).
		Where("food_id = ? AND merchant_no = ?", foodId, merchantNo).
		Delete(&model.FoodConfigModel{}).Error
	if err != nil {
		return err
	}

	// 做法配置
	foodConfigs := make([]*model.FoodConfigModel, 0, len(req.MethodConfigs)+len(req.AdditionConfigs))
	if req.MethodConfigs != nil && len(req.MethodConfigs) > 0 {
		for _, config := range req.MethodConfigs {
			foodConfigs = append(foodConfigs, &model.FoodConfigModel{
				MerchantNo:    merchantNo,
				FoodID:        foodId,
				GroupType:     model.GroupTypeMethod,
				GroupID:       config.GroupID,
				Required:      config.Required,
				RequiredCount: config.RequiredCount,
				UpperLimit:    config.UpperLimit,
				UpperCount:    config.UpperCount,
				RepeatChoice:  config.RepeatChoice,
			})
		}
	}
	// 加料配置
	if req.AdditionConfigs != nil && len(req.AdditionConfigs) > 0 {
		for _, config := range req.AdditionConfigs {
			foodConfigs = append(foodConfigs, &model.FoodConfigModel{
				MerchantNo:    merchantNo,
				FoodID:        foodId,
				GroupType:     model.GroupTypeAddition,
				GroupID:       config.GroupID,
				Required:      config.Required,
				RequiredCount: config.RequiredCount,
				UpperLimit:    config.UpperLimit,
				UpperCount:    config.UpperCount,
				RepeatChoice:  config.RepeatChoice,
			})
		}
	}
	if len(foodConfigs) > 0 {
		err = util.GetDB(ctx, serv.DB).Create(foodConfigs).Error
		if err != nil {
			return err
		}
	}

	return nil
}

// ChangeFoodState 更改食品状态
func (serv *FoodsService) ChangeFoodState(ctx context.Context, merchantNo string, foodId int64) (*model.FoodModel, error) {
	var food model.FoodModel

	// 查询食品记录
	one, err := util.FindOne(ctx, util.GetDB(ctx, serv.DB).
		Where("merchant_no = ? AND id = ?", merchantNo, foodId), &food)
	if err != nil {
		return nil, err
	}
	if !one {
		return nil, errors.NotFound("", "FoodNotFound")
	}

	// 更新状态（在0和1之间切换）
	if food.State == model.FoodStateNormal {
		food.State = model.FoodStateDisabled
	} else {
		food.State = model.FoodStateNormal
	}

	// 保存更改
	err = util.GetDB(ctx, serv.DB).Save(&food).Error
	if err != nil {
		return nil, err
	}

	return &food, nil
}

// ChangeFoodSupportScanOrder 更改食品是否支持扫码点单
func (serv *FoodsService) ChangeFoodSupportScanOrder(ctx context.Context, merchantNo string, foodId int64) (*model.FoodModel, error) {
	var food model.FoodModel

	// 查询食品记录
	one, err := util.FindOne(ctx, util.GetDB(ctx, serv.DB).
		Where("merchant_no = ? AND id = ?", merchantNo, foodId), &food)
	if err != nil {
		return nil, err
	}
	if !one {
		return nil, errors.NotFound("", "FoodNotFound")
	}

	// 更新支持扫码点单状态（取反）
	food.SupportScanOrder = !food.SupportScanOrder

	// 保存更改
	err = util.GetDB(ctx, serv.DB).Save(&food).Error
	if err != nil {
		return nil, err
	}

	return &food, nil
}

// DeleteFood 删除食品
func (serv *FoodsService) DeleteFood(ctx context.Context, merchantNo string, foodId int64) error {
	// 查询食品记录
	var food model.FoodModel
	// 查询食品记录
	one, err := util.FindOne(ctx, util.GetDB(ctx, serv.DB).
		Where("merchant_no = ? AND id = ?", merchantNo, foodId), &food)
	if err != nil {
		return err
	}
	if !one {
		return errors.NotFound("", "FoodNotFound")
	}

	// 检查是否有未支付的线上订单包含此食品
	var orderDetailCount int64
	err = util.GetDB(ctx, serv.DB).Model(&model.OrderDetailModel{}).
		Joins("JOIN cloud_orders ON order_details.order_id = cloud_orders.id").
		Where("order_details.food_id = ? AND cloud_orders.state < ?", foodId, consts.ORDER_STATE_PAID).
		Count(&orderDetailCount).Error
	if err != nil {
		return err
	}

	if orderDetailCount > 0 {
		return errors.BadRequest("", "ThisFoodHaveUnpaidOrder")
	}

	// 检查是否有套餐包含此食品
	var comboCount int64
	err = util.GetDB(ctx, serv.DB).Model(&model.FoodComboModel{}).
		Where("merchant_no = ? AND food_id = ?", merchantNo, foodId).
		Count(&comboCount).Error
	if err != nil {
		return err
	}

	if comboCount > 0 {
		return errors.BadRequest("", "FoodInComboFoods")
	}

	// 软删除
	now := time.Now()
	return util.GetDB(ctx, serv.DB).
		Model(&model.FoodModel{}).
		Where("id = ? AND merchant_no = ?", foodId, merchantNo).
		Updates(map[string]interface{}{
			"state":      model.FoodStateDisabled,
			"updated_at": now,
			"deleted_at": &now,
		}).Error
}

// GetFoodListForDualScreen 获取双屏显示食品列表
func (serv *FoodsService) GetFoodListForDualScreen(ctx context.Context, merchantNo string) ([]*model.FoodModel, error) {
	var foods []*model.FoodModel

	// 尝试从缓存获取数据
	cachedData, found, err := serv.Cache.Get(ctx, consts.CacheNSForDualScreen, merchantNo)
	if err != nil {
		return nil, err
	}
	if found {
		var cache []*model.FoodModel
		err := json.Unmarshal([]byte(cachedData), &cache)
		if err != nil {
			return nil, err
		}
		return cache, nil
	}

	// 如果缓存不存在或已过期，从数据库获取
	query := util.GetDB(ctx, serv.DB).
		Where("foods.merchant_no = ? AND foods.state = ?", merchantNo, model.FoodStateNormal).
		Select("foods.id, foods.name_ug, foods.name_zh, foods.image, foods.vip_price, foods.price").
		Limit(20).
		Order("foods.sort ASC, foods.created_at ASC")

	err = query.Find(&foods).Error
	if err != nil {
		return nil, err
	}

	// 将结果存入缓存（10分钟）
	cache := json.MarshalToString(foods)
	err = serv.Cache.Set(ctx, consts.CacheNSForDualScreen, merchantNo, cache, 10*time.Minute)
	if err != nil {
		return nil, err
	}

	return foods, nil
}

// GetDefaultFoodImages 获取系统默认提供的美食图片数据
func (serv *FoodsService) GetDefaultFoodImages(ctx context.Context) ([]*model.FoodsImageModel, error) {
	var images []*model.FoodsImageModel
	return images, util.GetDB(ctx, serv.DB).Find(&images).Error
}

// SaveSort 保存美食排序
func (serv *FoodsService) SaveSort(ctx context.Context, req *food_request.SaveFoodSortRequest, merchantNo string) error {
	return serv.Trans.Exec(ctx, func(ctx context.Context) error {
		// 批量验证美食是否属于当前商家
		foodIDs := make([]int64, 0, len(req.Items))
		for _, item := range req.Items {
			foodIDs = append(foodIDs, item.ID)
		}

		// 批量更新排序
		for _, item := range req.Items {
			err := util.GetDB(ctx, serv.DB).Model(&model.FoodModel{}).
				Where("id = ? AND merchant_no = ?", item.ID, merchantNo).
				Update("sort", item.Sort).Error
			if err != nil {
				return err
			}
		}
		return nil
	})
}

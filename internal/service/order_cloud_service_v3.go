package service

import (
	"context"
	"ros-api-go/internal/consts"
	"ros-api-go/internal/http/request/order_request"
	"ros-api-go/internal/model"
	"ros-api-go/pkg/errors"
	"ros-api-go/pkg/util"
)

// V3版本方法 - 支持规格、做法、加料和餐盒

// AddFoodsV3 v3版本加菜方法，支持规格、做法、加料和餐盒
func (serv *OrderCloudService) AddFoodsV3(
	ctx context.Context, merchantNo string, userID int64, userName string,
	orderId int64, req *order_request.OrderAddFoodRequestV3) (int64, []*model.OrderDetailModel, error) {

	// 根据订单ID获取订单信息。
	order, err := serv.GetCloudOrderByID(ctx, orderId, merchantNo)
	if err != nil {
		// 如果订单不存在，返回错误。
		return 0, nil, errors.BadRequest("", "OrderNotExist")
	}
	return serv.AddFoodsByOrderV3(ctx, order, merchantNo, userID, userName, req)
}

// AddFoodsByOrderV3 v3版本按订单加菜方法
func (serv *OrderCloudService) AddFoodsByOrderV3(
	ctx context.Context, order *model.CloudOrderModel,
	merchantNo string, userID int64, userName string,
	req *order_request.OrderAddFoodRequestV3,
) (int64, []*model.OrderDetailModel, error) {

	if order.State != consts.ORDER_STATE_NEW && order.State != consts.ORDER_STATE_UNPAID {
		// 如果订单状态无效，返回错误。
		return 0, nil, errors.BadRequest("", "OrderStateInvalid")
	}
	// 更新订单的备注信息。
	order.Remarks = req.OrderRemark
	// 初始化一个切片，用于存储订单详情。
	var detailsData []*model.OrderDetailModel
	err := serv.Trans.Exec(ctx, func(ctx context.Context) error {
		for _, v := range req.OrderDetails {
			// 第一步：创建主订单详情（规格级别）
			mainDetail, err := serv.NewFoodV3(ctx, order, merchantNo, userID, userName, v, 0) // 传入parentID=0表示顶级
			if err != nil {
				return err
			}

			// 保存主订单详情并获取ID
			err = util.GetDB(ctx, serv.DB).Create(mainDetail).Error
			if err != nil {
				return err
			}
			mainDetailID := mainDetail.ID // 获取主订单详情的ID
			detailsData = append(detailsData, mainDetail)

			// 第二步：处理套餐子项（如果是套餐）
			if mainDetail.IsCombo && mainDetail.ComboInfo != nil && len(*mainDetail.ComboInfo) > 0 {
				comboSubDetails, err := serv.createComboSubOrderDetails(ctx, order, merchantNo, userID, *mainDetail.ComboInfo, mainDetailID)
				if err != nil {
					return err
				}
				detailsData = append(detailsData, comboSubDetails...)
			}

			// 第三步：创建子级详情（加料、做法、餐盒）并设置Pid关联
			// 注意：createAllSubOrderDetails 方法内部已经处理了保存逻辑
			subDetails, err := serv.createAllSubOrderDetails(ctx, order, merchantNo, userID, v, mainDetailID)
			if err != nil {
				return err
			}

			// 将子级详情添加到返回数据中
			detailsData = append(detailsData, subDetails...)
		}
		// 如果订单状态为1，将其更新为2。
		if order.State == 1 {
			order.State = 2
		}
		// 更新订单信息。
		return serv.UpdateOrderData(ctx, order, merchantNo)
	})
	if err != nil {
		return 0, nil, errors.BadRequest("", "AddFoodFailed")
	}
	// 返回桌号和订单详情。
	return order.TableID, detailsData, nil
}

// NewFoodV3 v3版本创建食物订单详情，支持规格、做法、加料和餐盒
func (serv *OrderCloudService) NewFoodV3(ctx context.Context, order *model.CloudOrderModel, merchantNo string, userID int64, userName string, reqOrderDetail *order_request.OrderDetailV3, parentID int64) (*model.OrderDetailModel, error) {
	db := util.GetDB(ctx, serv.DB)

	// 获取基础食物信息（父级菜品）
	parentFood := model.FoodModel{}
	err := db.Model(parentFood).Where("id = ? and merchant_no = ?", reqOrderDetail.FoodID, merchantNo).Find(&parentFood).Error
	if err != nil {
		return nil, errors.BadRequest("", "FoodNotFound")
	}

	// 获取规格食物信息
	var specFood model.FoodModel
	if reqOrderDetail.SpecFoodID != 0 && reqOrderDetail.SpecFoodID != reqOrderDetail.FoodID {
		// 有具体规格
		err = db.Model(specFood).Where("id = ? and merchant_no = ? and pid = ?", reqOrderDetail.SpecFoodID, merchantNo, reqOrderDetail.FoodID).Find(&specFood).Error
		if err != nil {
			return nil, errors.BadRequest("", "FoodSpecNotFound")
		}
	} else {
		// 没有规格，使用父级菜品作为默认规格
		specFood = parentFood
		reqOrderDetail.SpecFoodID = reqOrderDetail.FoodID
	}

	// 处理库存清理
	if specFood.CellClearState {
		specFood.RemainingCount = util.SubtractFloat(specFood.RemainingCount, reqOrderDetail.FoodsCount)
		if specFood.RemainingCount < 0 {
			specFood.RemainingCount = 0
		}
		db.Select("remaining_count").Save(&specFood)
	}

	// 创建主订单详情（规格级别）
	mainOrderDetail := &model.OrderDetailModel{
		ID:            0,
		MerchantNo:    merchantNo,
		OrderNo:       order.No,
		Pid:           parentID, // 设置父级ID
		FoodID:        reqOrderDetail.FoodID,
		SpecFoodID:    reqOrderDetail.SpecFoodID,
		Type:          model.FoodTypeFood, // 主菜品类型
		FoodsCount:    reqOrderDetail.FoodsCount,
		CostPrice:     specFood.CostPrice,
		OriginalPrice: specFood.Price,
		VipPrice:      specFood.VipPrice,
		Price:         specFood.Price,
		TotalPrice:    util.Round(util.MultiplyFloat(reqOrderDetail.FoodsCount, specFood.Price)),
		IsPrint:       false,
		UserID:        userID,
		State:         2,
		Remarks:       &reqOrderDetail.Remarks,
		IsCombo:       specFood.IsCombo,
		ComboInfo:     reqOrderDetail.ComboInfo,
		IsSync:        false,
	}

	// 扫码点菜不需要保存OrderId, 普通订单需要
	if !order.IsScanOrder {
		mainOrderDetail.OrderID = order.ID
	}
	mainOrderDetail.Food = &specFood

	return mainOrderDetail, nil
}

// createAllSubOrderDetails 创建所有子级订单详情（加料、做法、餐盒）
func (serv *OrderCloudService) createAllSubOrderDetails(ctx context.Context, order *model.CloudOrderModel, merchantNo string, userID int64, reqOrderDetail *order_request.OrderDetailV3, parentID int64) ([]*model.OrderDetailModel, error) {
	var allSubDetails []*model.OrderDetailModel
	db := util.GetDB(ctx, serv.DB)

	// 第一步：处理加料（作为子级）
	var additionDetails []*model.OrderDetailModel
	if reqOrderDetail.Additions != nil && len(reqOrderDetail.Additions) > 0 {
		for _, addition := range reqOrderDetail.Additions {
			additionDetail, err := serv.createSingleSubOrderDetail(ctx, order, merchantNo, userID, addition, model.FoodTypeAddition, parentID)
			if err != nil {
				return nil, err
			}
			// 保存加料详情以获取ID
			err = db.Create(additionDetail).Error
			if err != nil {
				return nil, err
			}
			additionDetails = append(additionDetails, additionDetail)
			allSubDetails = append(allSubDetails, additionDetail)

			// 递归处理加料的子级
			subSubDetails, err := serv.createAllSubOrderDetails(ctx, order, merchantNo, userID, addition, additionDetail.ID)
			if err != nil {
				return nil, err
			}
			allSubDetails = append(allSubDetails, subSubDetails...)
		}
	}

	// 第二步：处理做法（作为子级）
	if reqOrderDetail.Methods != nil && len(reqOrderDetail.Methods) > 0 {
		for _, method := range reqOrderDetail.Methods {
			methodDetail, err := serv.createMethodOrderDetailWithPid(ctx, order, merchantNo, userID, method, parentID)
			if err != nil {
				return nil, err
			}
			// 保存做法详情
			err = db.Create(methodDetail).Error
			if err != nil {
				return nil, err
			}
			allSubDetails = append(allSubDetails, methodDetail)
		}
	}

	// 第三步：处理餐盒（作为子级）
	var lunchBoxDetails []*model.OrderDetailModel
	if reqOrderDetail.LunchBoxes != nil && len(reqOrderDetail.LunchBoxes) > 0 {
		for _, lunchBox := range reqOrderDetail.LunchBoxes {
			lunchBoxDetail, err := serv.createSingleSubOrderDetail(ctx, order, merchantNo, userID, lunchBox, model.FoodTypeLunchBox, parentID)
			if err != nil {
				return nil, err
			}
			// 保存餐盒详情以获取ID
			err = db.Create(lunchBoxDetail).Error
			if err != nil {
				return nil, err
			}
			lunchBoxDetails = append(lunchBoxDetails, lunchBoxDetail)
			allSubDetails = append(allSubDetails, lunchBoxDetail)

			// 递归处理餐盒的子级
			subSubDetails, err := serv.createAllSubOrderDetails(ctx, order, merchantNo, userID, lunchBox, lunchBoxDetail.ID)
			if err != nil {
				return nil, err
			}
			allSubDetails = append(allSubDetails, subSubDetails...)
		}
	}

	return allSubDetails, nil
}

// createComboSubOrderDetails 创建套餐子项的订单详情
func (serv *OrderCloudService) createComboSubOrderDetails(ctx context.Context, order *model.CloudOrderModel, merchantNo string, userID int64, comboInfos []model.ComboInfo, parentID int64) ([]*model.OrderDetailModel, error) {
	var allComboSubDetails []*model.OrderDetailModel
	db := util.GetDB(ctx, serv.DB)

	for _, comboInfo := range comboInfos {
		// 获取套餐子项的食物信息
		var comboFood model.FoodModel
		err := db.Model(comboFood).Where("id = ? and merchant_no = ?", comboInfo.FoodID, merchantNo).Find(&comboFood).Error
		if err != nil {
			return nil, errors.BadRequest("", "FoodNotFound")
		}

		// 获取规格食物信息
		var specFood model.FoodModel
		if comboInfo.SpecFoodID != 0 && comboInfo.SpecFoodID != comboInfo.FoodID {
			// 有具体规格
			err = db.Model(specFood).Where("id = ? and merchant_no = ? and pid = ?", comboInfo.SpecFoodID, merchantNo, comboInfo.FoodID).Find(&specFood).Error
			if err != nil {
				return nil, errors.BadRequest("", "FoodSpecNotFound")
			}
		} else {
			// 没有规格，使用父级菜品作为默认规格
			specFood = comboFood
			comboInfo.SpecFoodID = comboInfo.FoodID
		}

		// 创建套餐子项订单详情
		comboSubDetail := &model.OrderDetailModel{
			ID:            0,
			MerchantNo:    merchantNo,
			OrderNo:       order.No,
			Pid:           parentID, // 设置套餐主记录的ID作为父级
			FoodID:        comboInfo.FoodID,
			SpecFoodID:    comboInfo.SpecFoodID,
			Type:          model.FoodTypeFood, // 套餐子项也是美食类型
			FoodsCount:    comboInfo.Count,
			CostPrice:     specFood.CostPrice,
			OriginalPrice: specFood.Price,
			VipPrice:      specFood.VipPrice,
			Price:         specFood.Price,
			TotalPrice:    util.Round(util.MultiplyFloat(comboInfo.Count, specFood.Price)),
			IsPrint:       false,
			UserID:        userID,
			State:         2,
			Remarks:       &comboInfo.Remarks,
			IsCombo:       false, // 套餐子项不是套餐
			IsSync:        false,
		}

		// 扫码点菜不需要保存OrderId, 普通订单需要
		if !order.IsScanOrder {
			comboSubDetail.OrderID = order.ID
		}
		comboSubDetail.Food = &specFood

		// 保存套餐子项详情以获取ID
		err = db.Create(comboSubDetail).Error
		if err != nil {
			return nil, err
		}
		allComboSubDetails = append(allComboSubDetails, comboSubDetail)

		// 处理套餐子项的加料
		if len(comboInfo.Additions) > 0 {
			for _, addition := range comboInfo.Additions {
				additionDetail, err := serv.createComboItemOrderDetail(ctx, order, merchantNo, userID, addition, model.FoodTypeAddition, comboSubDetail.ID)
				if err != nil {
					return nil, err
				}
				err = db.Create(additionDetail).Error
				if err != nil {
					return nil, err
				}
				allComboSubDetails = append(allComboSubDetails, additionDetail)
			}
		}

		// 处理套餐子项的做法
		if len(comboInfo.Methods) > 0 {
			for _, method := range comboInfo.Methods {
				methodDetail, err := serv.createComboMethodOrderDetail(ctx, order, merchantNo, userID, method, comboSubDetail.ID)
				if err != nil {
					return nil, err
				}
				err = db.Create(methodDetail).Error
				if err != nil {
					return nil, err
				}
				allComboSubDetails = append(allComboSubDetails, methodDetail)
			}
		}

		// 处理套餐子项的餐盒
		if len(comboInfo.LunchBoxes) > 0 {
			for _, lunchBox := range comboInfo.LunchBoxes {
				lunchBoxDetail, err := serv.createComboItemOrderDetail(ctx, order, merchantNo, userID, lunchBox, model.FoodTypeLunchBox, comboSubDetail.ID)
				if err != nil {
					return nil, err
				}
				err = db.Create(lunchBoxDetail).Error
				if err != nil {
					return nil, err
				}
				allComboSubDetails = append(allComboSubDetails, lunchBoxDetail)
			}
		}
	}

	return allComboSubDetails, nil
}

// createComboItemOrderDetail 创建套餐子项的加料或餐盒订单详情
func (serv *OrderCloudService) createComboItemOrderDetail(ctx context.Context, order *model.CloudOrderModel, merchantNo string, userID int64, itemInfo model.ComboItemInfo, foodType int64, parentID int64) (*model.OrderDetailModel, error) {
	db := util.GetDB(ctx, serv.DB)

	// 获取加料或餐盒的食物信息
	var itemFood model.FoodModel
	err := db.Model(itemFood).Where("id = ? and merchant_no = ? and type = ?", itemInfo.FoodID, merchantNo, foodType).Find(&itemFood).Error
	if err != nil {
		if foodType == model.FoodTypeAddition {
			return nil, errors.BadRequest("", "FoodAdditionNotFound")
		} else {
			return nil, errors.BadRequest("", "LunchBoxNotFound")
		}
	}

	// 获取规格食物信息
	var specFood model.FoodModel
	if itemInfo.SpecFoodID != 0 && itemInfo.SpecFoodID != itemInfo.FoodID {
		// 有具体规格
		err = db.Model(specFood).Where("id = ? and merchant_no = ? and pid = ?", itemInfo.SpecFoodID, merchantNo, itemInfo.FoodID).Find(&specFood).Error
		if err != nil {
			return nil, errors.BadRequest("", "FoodSpecNotFound")
		}
	} else {
		// 没有规格，使用父级菜品作为默认规格
		specFood = itemFood
		itemInfo.SpecFoodID = itemInfo.FoodID
	}

	// 创建加料或餐盒订单详情
	itemOrderDetail := &model.OrderDetailModel{
		ID:            0,
		MerchantNo:    merchantNo,
		OrderNo:       order.No,
		Pid:           parentID, // 设置套餐子项ID作为父级
		FoodID:        itemInfo.FoodID,
		SpecFoodID:    itemInfo.SpecFoodID,
		Type:          foodType,
		FoodsCount:    itemInfo.Count,
		CostPrice:     specFood.CostPrice,
		OriginalPrice: specFood.Price,
		VipPrice:      specFood.VipPrice,
		Price:         specFood.Price,
		TotalPrice:    util.Round(util.MultiplyFloat(itemInfo.Count, specFood.Price)),
		IsPrint:       false,
		UserID:        userID,
		State:         2,
		Remarks:       &itemInfo.Remarks,
		IsCombo:       false,
		IsSync:        false,
	}

	// 扫码点菜不需要保存OrderId, 普通订单需要
	if !order.IsScanOrder {
		itemOrderDetail.OrderID = order.ID
	}
	itemOrderDetail.Food = &specFood

	return itemOrderDetail, nil
}

// createComboMethodOrderDetail 创建套餐子项的做法订单详情
func (serv *OrderCloudService) createComboMethodOrderDetail(ctx context.Context, order *model.CloudOrderModel, merchantNo string, userID int64, methodInfo model.ComboMethodInfo, parentID int64) (*model.OrderDetailModel, error) {
	db := util.GetDB(ctx, serv.DB)

	// 验证做法是否存在
	var methodModel model.MethodModel
	err := db.Where("id = ? AND merchant_no = ? AND state = ?", methodInfo.MethodID, merchantNo, model.MethodStateNormal).First(&methodModel).Error
	if err != nil {
		return nil, errors.BadRequest("", "MethodNotFound")
	}

	// 验证做法分组是否匹配
	if methodInfo.GroupID != 0 && methodModel.GroupID != methodInfo.GroupID {
		return nil, errors.BadRequest("", "MethodGroupMismatch")
	}

	// 创建做法订单详情
	methodOrderDetail := &model.OrderDetailModel{
		ID:            0,
		MerchantNo:    merchantNo,
		OrderNo:       order.No,
		Pid:           parentID, // 设置套餐子项ID作为父级
		MethodID:      methodInfo.MethodID,
		Type:          4, // 做法类型
		FoodsCount:    float64(methodInfo.Count),
		CostPrice:     0, // 做法通常没有成本价
		OriginalPrice: methodModel.Price,
		VipPrice:      methodModel.Price,
		Price:         methodModel.Price,
		TotalPrice:    util.Round(util.MultiplyFloat(float64(methodInfo.Count), methodModel.Price)),
		IsPrint:       false,
		UserID:        userID,
		State:         2,
		IsCombo:       false,
		IsSync:        false,
	}

	// 扫码点菜不需要保存OrderId, 普通订单需要
	if !order.IsScanOrder {
		methodOrderDetail.OrderID = order.ID
	}

	return methodOrderDetail, nil
}

// createSingleSubOrderDetail 创建单个子级订单详情（不处理递归）
func (serv *OrderCloudService) createSingleSubOrderDetail(ctx context.Context, order *model.CloudOrderModel, merchantNo string, userID int64, subDetail *order_request.OrderDetailV3, foodType int64, parentID int64) (*model.OrderDetailModel, error) {
	db := util.GetDB(ctx, serv.DB)

	// 获取子级食物信息
	var subFood model.FoodModel
	err := db.Model(subFood).Where("id = ? and merchant_no = ? and type = ?", subDetail.FoodID, merchantNo, foodType).Find(&subFood).Error
	if err != nil {
		if foodType == model.FoodTypeAddition {
			return nil, errors.BadRequest("", "FoodAdditionNotFound")
		} else {
			return nil, errors.BadRequest("", "LunchBoxNotFound")
		}
	}

	// 创建子级订单详情
	subOrderDetail := &model.OrderDetailModel{
		ID:            0,
		MerchantNo:    merchantNo,
		OrderNo:       order.No,
		Pid:           parentID, // 设置父级ID关联
		FoodID:        subDetail.FoodID,
		Type:          foodType,
		FoodsCount:    subDetail.FoodsCount,
		CostPrice:     subFood.CostPrice,
		OriginalPrice: subFood.Price,
		VipPrice:      subFood.VipPrice,
		Price:         subFood.Price,
		TotalPrice:    util.Round(util.MultiplyFloat(subDetail.FoodsCount, subFood.Price)),
		IsPrint:       false,
		UserID:        userID,
		State:         2,
		Remarks:       &subDetail.Remarks,
		IsCombo:       false,
		IsSync:        false,
	}

	// 扫码点菜不需要保存OrderId, 普通订单需要
	if !order.IsScanOrder {
		subOrderDetail.OrderID = order.ID
	}
	subOrderDetail.Food = &subFood

	return subOrderDetail, nil
}

// createMethodOrderDetailWithPid 创建做法订单详情并设置Pid关联
func (serv *OrderCloudService) createMethodOrderDetailWithPid(ctx context.Context, order *model.CloudOrderModel, merchantNo string, userID int64, method *order_request.OrderDetailMethodV3, parentID int64) (*model.OrderDetailModel, error) {
	db := util.GetDB(ctx, serv.DB)

	// 验证做法是否存在
	var methodModel model.MethodModel
	err := db.Where("id = ? AND merchant_no = ? AND state = ?", method.MethodID, merchantNo, model.MethodStateNormal).First(&methodModel).Error
	if err != nil {
		return nil, errors.BadRequest("", "MethodNotFound")
	}

	// 验证做法分组是否匹配
	if method.GroupID != 0 && methodModel.GroupID != method.GroupID {
		return nil, errors.BadRequest("", "MethodGroupMismatch")
	}

	// 创建做法订单详情
	methodOrderDetail := &model.OrderDetailModel{
		ID:            0,
		MerchantNo:    merchantNo,
		OrderNo:       order.No,
		Pid:           parentID, // 设置父级ID关联
		MethodID:      method.MethodID,
		Type:          model.OrderDetailTypeMethod, // 做法类型
		FoodsCount:    1,
		CostPrice:     0, // 做法通常没有成本价
		OriginalPrice: methodModel.Price,
		VipPrice:      methodModel.Price,
		Price:         methodModel.Price,
		TotalPrice:    methodModel.Price,
		IsPrint:       false,
		UserID:        userID,
		State:         2,
		IsCombo:       false,
		IsSync:        false,
	}

	// 扫码点菜不需要保存OrderId, 普通订单需要
	if !order.IsScanOrder {
		methodOrderDetail.OrderID = order.ID
	}

	return methodOrderDetail, nil
}

// V3版本退菜方法

// CancelFoodV3 v3版本退菜方法，支持基于Pid关联关系的退菜
func (serv *OrderCloudService) CancelFoodV3(ctx context.Context, userID int64, userName string, merchantNo string, orderId int64, orderDetailID int64, req *order_request.CancelFoodV3Request) (int64, []*model.OrderDetailModel, error) {
	// 根据订单ID获取订单信息
	order, err := serv.GetCloudOrderByID(ctx, orderId, merchantNo)
	if err != nil {
		return 0, nil, errors.BadRequest("", "OrderNotExist")
	}

	// 检查订单状态是否允许取消
	if order.State != 1 && order.State != 2 {
		return 0, nil, errors.BadRequest("", "OrderStateNotAllowCancel")
	}

	// 根据订单详情ID获取订单详情信息
	od, err := serv.GetCloudOrderDetailByID(ctx, orderDetailID, merchantNo)
	if err != nil {
		return 0, nil, errors.BadRequest("", "OrderDetailNotFound")
	}

	// 检查菜品状态是否允许取消
	if od.State == 3 || od.State == 4 {
		return 0, nil, errors.BadRequest("", "FoodStateNotAllowCancel")
	}

	// 验证退菜数量
	if req.FoodsCount > od.FoodsCount {
		return 0, nil, errors.BadRequest("", "CancelQuantityExceed")
	}

	// 检查是否为套餐子项（不允许单独退套餐子项）
	if od.Pid != 0 {
		// 检查父级是否为套餐
		parentOd, err := serv.GetCloudOrderDetailByID(ctx, od.Pid, merchantNo)
		if err == nil && parentOd.IsCombo {
			return 0, nil, errors.BadRequest("", "CannotCancelComboSubItem")
		}
	}

	var cancelledDetails []*model.OrderDetailModel

	// 使用事务执行退菜操作
	err = serv.Trans.Exec(ctx, func(ctx context.Context) error {
		// 默认使用单独退菜模式
		cancelledDetail, err := serv.cancelSingleFood(ctx, od, req, userID)
		if err != nil {
			return err
		}
		cancelledDetails = append(cancelledDetails, cancelledDetail)

		// 更新订单数据
		return serv.UpdateOrderData(ctx, order, merchantNo)
	})

	if err != nil {
		return 0, nil, err
	}

	return order.TableID, cancelledDetails, nil
}

// cancelSingleFood 单独退菜（只退指定的OrderDetail记录）
func (serv *OrderCloudService) cancelSingleFood(ctx context.Context, od *model.OrderDetailModel, req *order_request.CancelFoodV3Request, userID int64) (*model.OrderDetailModel, error) {
	db := util.GetDB(ctx, serv.DB)

	// 处理库存回退
	if od.Food != nil && od.Food.CellClearState {
		od.Food.RemainingCount = util.AddFloat(od.Food.RemainingCount, req.FoodsCount)
		err := db.Select("remaining_count").Save(od.Food).Error
		if err != nil {
			return nil, err
		}
	}

	// 如果是部分退菜，需要更新原记录并创建新的退菜记录
	if req.FoodsCount < od.FoodsCount {
		// 更新原记录的数量和总价
		od.FoodsCount = util.SubtractFloat(od.FoodsCount, req.FoodsCount)
		od.TotalPrice = util.MultiplyFloat(od.FoodsCount, od.OriginalPrice)

		err := db.Save(od).Error
		if err != nil {
			return nil, err
		}

		// 创建新的退菜记录
		cancelledDetail := &model.OrderDetailModel{
			ID:            0, // 新记录，ID为0
			MerchantNo:    od.MerchantNo,
			OrderNo:       od.OrderNo,
			OrderID:       od.OrderID,
			Pid:           od.Pid,
			FoodID:        od.FoodID,
			SpecFoodID:    od.SpecFoodID,
			MethodID:      od.MethodID,
			Type:          od.Type,
			FoodsCount:    req.FoodsCount,
			CostPrice:     od.CostPrice,
			OriginalPrice: od.OriginalPrice,
			VipPrice:      od.VipPrice,
			Price:         od.Price,
			TotalPrice:    util.MultiplyFloat(req.FoodsCount, od.OriginalPrice),
			IsPrint:       false,
			UserID:        userID,
			State:         4, // 退菜状态
			Remarks:       req.Remarks,
			IsCombo:       od.IsCombo,
			ComboInfo:     od.ComboInfo,
			IsSync:        false,
		}

		err = db.Create(cancelledDetail).Error
		if err != nil {
			return nil, err
		}

		return cancelledDetail, nil
	} else {
		// 全部退菜，直接更新状态
		od.State = 4 // 退菜状态
		od.UserID = userID
		od.Remarks = req.Remarks

		err := db.Save(od).Error
		if err != nil {
			return nil, err
		}

		return od, nil
	}
}

// cancelFoodWithChildren 连带退菜（退主菜品及其所有子级）
func (serv *OrderCloudService) cancelFoodWithChildren(ctx context.Context, od *model.OrderDetailModel, req *order_request.CancelFoodV3Request, userID int64) ([]*model.OrderDetailModel, error) {
	var allCancelledDetails []*model.OrderDetailModel

	// 首先退主菜品
	mainCancelledDetail, err := serv.cancelSingleFood(ctx, od, req, userID)
	if err != nil {
		return nil, err
	}
	allCancelledDetails = append(allCancelledDetails, mainCancelledDetail)

	// 查找并退掉所有子级记录（递归处理）
	childCancelledDetails, err := serv.cancelChildrenRecursively(ctx, od.ID, od.MerchantNo, userID)
	if err != nil {
		return nil, err
	}
	allCancelledDetails = append(allCancelledDetails, childCancelledDetails...)

	return allCancelledDetails, nil
}

// cancelChildrenRecursively 递归退掉所有子级记录
func (serv *OrderCloudService) cancelChildrenRecursively(ctx context.Context, parentID int64, merchantNo string, userID int64) ([]*model.OrderDetailModel, error) {
	db := util.GetDB(ctx, serv.DB)
	var allCancelledChildren []*model.OrderDetailModel

	// 查找所有直接子级记录
	var children []*model.OrderDetailModel
	err := db.Where("pid = ? AND merchant_no = ? AND state IN ?", parentID, merchantNo, []int{1, 2}).
		Preload("Food").Find(&children).Error
	if err != nil {
		return nil, err
	}

	for _, child := range children {
		// 处理库存回退
		if child.Food != nil && child.Food.CellClearState {
			child.Food.RemainingCount = util.AddFloat(child.Food.RemainingCount, child.FoodsCount)
			err := db.Select("remaining_count").Save(child.Food).Error
			if err != nil {
				return nil, err
			}
		}

		// 更新子级记录状态为退菜
		child.State = 4 // 退菜状态
		child.UserID = userID
		emptyRemarks := "连带退菜"
		child.Remarks = &emptyRemarks

		err := db.Save(child).Error
		if err != nil {
			return nil, err
		}
		allCancelledChildren = append(allCancelledChildren, child)

		// 递归处理子级的子级
		grandChildren, err := serv.cancelChildrenRecursively(ctx, child.ID, merchantNo, userID)
		if err != nil {
			return nil, err
		}
		allCancelledChildren = append(allCancelledChildren, grandChildren...)
	}

	return allCancelledChildren, nil
}

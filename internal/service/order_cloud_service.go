package service

import (
	"context"
	"fmt"
	"github.com/gin-gonic/gin"
	"github.com/redis/go-redis/v9"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"ros-api-go/internal/consts"
	"ros-api-go/internal/http/request/food_request"
	"ros-api-go/internal/http/request/order_request"
	"ros-api-go/internal/http/request/scan_request"
	"ros-api-go/internal/model"
	"ros-api-go/pkg/errors"
	"ros-api-go/pkg/logging"
	"ros-api-go/pkg/util"
	"strconv"
	"time"
)

type OrderCloudService struct {
	DB          *gorm.DB
	Trans       *util.Trans
	RedisClient *redis.Client
}

// GetCloudOrderByID 根据ID获取订单
func (serv *OrderCloudService) GetCloudOrderByID(ctx context.Context, id int64, merchantNo string) (*model.CloudOrderModel, error) {
	var order model.CloudOrderModel
	ok, err := util.FindOne(ctx, util.GetDB(ctx, serv.DB).
		Where("id =?", id).Where("merchant_no = ?", merchantNo), &order)
	if err != nil {
		return nil, err
	} else if !ok {
		return nil, nil
	}
	return &order, nil
}

// NotSyncOrdersCount 未同步订单数量
func (serv *OrderCloudService) NotSyncOrdersCount(ctx context.Context, merchantNo string, userId int64) (int64, error) {
	var count int64
	return count, util.GetDB(ctx, serv.DB).Model(&model.Order{}).
		Where("merchant_no = ? and is_sync = ? and state IN ? AND cashier_id = ?", merchantNo, 0, []int{3, 4, 5}, userId).Count(&count).Error
}

// OrderSynced 更新订单同步状态
func (serv *OrderCloudService) OrderSynced(ctx context.Context, orderID int64, merchantNo string) error {
	return util.GetDB(ctx, serv.DB).Model(&model.CloudOrderModel{}).
		Where("merchant_no = ? and id = ?", merchantNo, orderID).
		Update("is_sync", true).Error
}

// GetCloudOrderByNo 根据订单号获取订单
func (serv *OrderCloudService) GetCloudOrderByNo(ctx context.Context, orderNo string, merchantNo string) (*model.CloudOrderModel, error) {
	var order model.CloudOrderModel
	ok, err := util.FindOne(ctx, util.GetDB(ctx, serv.DB).
		Where("no =?", orderNo).Where("merchant_no = ?", merchantNo), &order)
	if err != nil {
		return nil, err
	} else if !ok {
		return nil, nil
	}
	return &order, nil
}

// GetCloudOrderByID 根据ID获取订单详细
func (serv *OrderCloudService) GetCloudOrderDetailByID(ctx context.Context, id int64, merchantNo string) (*model.OrderDetailModel, error) {
	var od model.OrderDetailModel
	ok, err := util.FindOne(ctx, util.GetDB(ctx, serv.DB).Preload("Food").
		Where("id =?", id).Where("merchant_no = ?", merchantNo), &od)
	if err != nil {
		return nil, err
	} else if !ok {
		return nil, nil
	}
	return &od, nil
}

// GetCloudOrderDetailByOrderNo 根据OrderNo获取订单详细
func (serv *OrderCloudService) GetCloudOrderDetailByOrderNo(ctx context.Context, orderNo string, state []int, merchantNo string) ([]*model.OrderDetailModel, error) {
	var od []*model.OrderDetailModel
	err := util.GetDB(ctx, serv.DB).
		Where("order_no =? and state IN ?", orderNo, state).Preload("Food").Where("merchant_no = ?", merchantNo).Find(&od).Error
	if err != nil {
		return nil, err
	}
	return od, nil
}

// TableList 餐桌列表
func (serv *OrderCloudService) TableList(c context.Context, merchantNo string, area_id string, status string, keyword string) ([]model.TableDataModel, int, int, int, int, float64, error) {
	areaIdInt, _ := strconv.Atoi(area_id)

	// 调用TableDal的TableList方法获取桌台列表数据
	var tablesData []model.TableDataModel

	query := util.GetDB(c, serv.DB)

	query = query.Model(&model.TableDataModel{}).Preload("Orders", "merchant_no = ? AND state IN ?", merchantNo, []int{1, 2})

	query = query.Where("state = 1 and merchant_no = ?", merchantNo)
	// 构建动态查询条件
	if area_id != "" && areaIdInt != 0 {
		query = query.Where("area_id = ?", areaIdInt)
	}
	if keyword != "" {
		query = query.Where("(name_ug LIKE ? OR name_zh LIKE ?)", "%"+keyword+"%", "%"+keyword+"%")
	}
	query = query.Order("sort asc")

	// 执行查询
	err := query.Find(&tablesData).Error
	if err != nil {
		return nil, 0, 0, 0, 0, 0, err
	}

	// 初始化OrderAmount变量，用于累计所有桌台的订单金额
	var OrderAmount float64 = 0

	// 遍历每个桌台，获取其订单信息并更新桌台状态
	for i := range tablesData {
		table := (tablesData)[i]

		// 获取指定桌台的订单列表
		orders := table.Orders

		// 更新桌台的订单数量
		(tablesData)[i].OrdersCount = len(orders)

		// 初始化桌台状态为1（空闲）
		(tablesData)[i].TableStatus = 1

		// 如果桌台有订单，则将其状态更新为2（有顾客）
		if (tablesData)[i].OrdersCount > 0 {
			(tablesData)[i].TableStatus = 2
		}

		// 遍历每个订单，累计订单金额并更新桌台的顾客数量和订单详情数量
		for _, order := range orders {
			OrderAmount = util.AddFloat(OrderAmount, order.Price)

			tablesData[i].CustomersCount = tablesData[i].CustomersCount + order.CustomersCount
			tablesData[i].OrderDetailsCount = tablesData[i].OrderDetailsCount + int(order.FoodsCount)

			// 如果桌台状态不是3（有订单）且有订单详情，则将其状态更新为3
			if tablesData[i].TableStatus != 3 && order.FoodsCount > 0 {
				tablesData[i].TableStatus = 3
			}

		}
	}

	// 初始化统计变量，用于记录不同状态的桌台数量和订单数量
	EmptyTableCount := 0
	HasCustomersTableCount := 0
	HasOrderTableCount := 0
	OrderCount := 0

	// 遍历所有桌台，统计不同状态的桌台数量和订单数量
	for i := 0; i < len(tablesData); i++ {
		if tablesData[i].TableStatus == 1 {
			EmptyTableCount++
		}
		if tablesData[i].TableStatus == 2 {
			HasCustomersTableCount++
		}
		if tablesData[i].TableStatus == 3 {
			HasOrderTableCount++
		}
		OrderCount = OrderCount + tablesData[i].OrdersCount
	}

	// 如果传入了status参数且不为"0"，则根据status筛选桌台列表
	if status != "" && status != "0" {
		statusInt, _ := strconv.Atoi(status)
		for i := 0; i < len(tablesData); i++ {
			if tablesData[i].TableStatus != statusInt {
				tablesData = append(tablesData[:i], tablesData[i+1:]...)
				i--
			}
		}
	}

	return tablesData, EmptyTableCount, HasCustomersTableCount, HasOrderTableCount, OrderCount, OrderAmount, err
}

// CreateOrder 创建订单
func (serv *OrderCloudService) CreateOrder(ctx context.Context, merchantNo string, userID int64, userName string, customersCount int, tableId int64, terminal string) (*model.CloudOrderModel, error) {
	// 将顾客数量和桌号从字符串转换为整数
	OrderNo, err := serv.getOrderNo(ctx)
	if err != nil {
		return nil, errors.BadRequest("", "CreateOrderFailed")
	}

	// 初始化订单对象
	order := &model.CloudOrderModel{}
	// 设置订单的各个字段
	order.ID = 0
	order.MerchantNo = merchantNo
	order.No = OrderNo
	order.TableID = tableId
	order.TerminalID = 1
	order.CustomersCount = customersCount
	order.FoodsCount = 0
	order.CostPrice = 0
	order.OriginalPrice = 0
	order.VipPrice = 0
	order.Price = 0
	order.CollectedAmount = 0
	order.User = userName
	order.UserID = userID
	order.CashierID = 0
	order.GiveChange = 0
	order.State = 1
	order.Remarks = ""
	order.TaxTicket = false
	order.IsPrint = false
	order.IsScanOrder = false

	//调用OrderDal的CreateOrder方法创建订单
	if util.GetDB(ctx, serv.DB).Create(order).Error != nil {
		// 如果创建订单失败，返回错误信息
		return nil, errors.BadRequest("", "CreateOrderFailed")
	}
	// 如果创建订单成功，返回订单对象
	return order, nil
}

// getOrderNo 获取订单号
func (serv *OrderCloudService) getOrderNo(ctx context.Context) (string, error) {
	cacheKey := "ros:order_no_" + time.Now().Format("20060102")
	index := serv.RedisClient.Incr(ctx, cacheKey)
	if index.Err() != nil {
		return "", errors.BadRequest("", "GetOrderNoFailed")
	}
	indexInt, _ := index.Result()
	if indexInt == 1 {
		// 获取当前时间
		now := time.Now()

		// 获取明天的日期
		tomorrow := time.Date(now.Year(), now.Month(), now.Day()+1, 0, 0, 0, 0, now.Location())

		// 在明天的基础上加上 30 分钟
		expireTime := tomorrow.Add(30 * time.Minute)

		// 计算现在到目标时间的秒数差
		expireTime.Sub(now).Seconds()
		diff := expireTime.Sub(now).Seconds()
		serv.RedisClient.Expire(ctx, cacheKey, time.Duration(diff)*time.Second)

	}
	formattedTime := time.Now().Format("0601021504")
	return fmt.Sprintf("%s%03d", formattedTime, indexInt), nil

}

// TableOrders 获取桌台订单
func (serv *OrderCloudService) TableOrders(ctx context.Context, merchantNo string, id string) (*model.TableModel, []*model.FoodCategoryModel, []*model.FoodModel, error) {

	// 通过餐桌ID获取餐桌信息
	table := &model.TableModel{
		ID: util.StrToInt64(id),
	}

	query := util.GetDB(ctx, serv.DB)

	query = query.Preload("CloudOrders", func(db *gorm.DB) *gorm.DB {
		return db.Where("merchant_no = ? and state IN ?", merchantNo, []int{1, 2}).
			Preload("Details", func(db *gorm.DB) *gorm.DB {
				return db.Preload("Food").Preload("Creator")
			}).Preload("Creator")
	})

	err := query.First(table).Error
	if err != nil {
		return nil, nil, nil, errors.BadRequest("", "TableNotFound")
	}

	//获取美食分类
	categoryModel := []*model.FoodCategoryModel{}
	err = util.GetDB(ctx, serv.DB).Model(model.FoodCategoryModel{}).
		Where("merchant_no = ?", merchantNo).
		Find(&categoryModel).Error
	if err != nil {
		return nil, nil, nil, errors.BadRequest("", "GetFoodCategoryFailed")
	}

	categoryModel = append([]*model.FoodCategoryModel{{
		ID:         0,
		MerchantNo: merchantNo,
		NameZh:     "全部",
		NameUg:     "ھەممىسى",
		Sort:       0,
		State:      1,
	}}, categoryModel...)

	//获取美食
	foodModel := []*model.FoodModel{}
	err = util.GetDB(ctx, serv.DB).Model(model.FoodModel{}).
		Where("merchant_no = ? AND state = 1", merchantNo).
		Find(&foodModel).Error
	if err != nil {
		return nil, nil, nil, errors.BadRequest("", "GetFoodListFailed")
	}

	return table, categoryModel, foodModel, nil
}

// OrderDetails 获取订单详情
func (serv *OrderCloudService) OrderDetails(ctx context.Context, merchantNo string, orderId int64) (*model.CloudOrderModel, error) {

	// 通过餐桌ID获取餐桌信息
	order := &model.CloudOrderModel{
		ID: orderId,
	}

	query := util.GetDB(ctx, serv.DB)

	query = query.Model(order).Where("merchant_no = ? and state IN ?", merchantNo, []int{1, 2}).
		Preload("Details", func(db *gorm.DB) *gorm.DB {
			return db.Where("merchant_no = ? and state IN ?", merchantNo, []int{1, 2})
		}).
		Preload("CanceledFoods", func(db *gorm.DB) *gorm.DB {
			return db.Where("merchant_no = ? and state IN ?", merchantNo, []int{4})
		})

	err := query.First(order).Error
	if err != nil {
		return nil, errors.BadRequest("", "OrderNotFound")
	}

	// 返回整合了订单详情的订单对象。
	return order, nil
}

func (serv *OrderCloudService) AddFoods(
	ctx context.Context, merchantNo string, userID int64, userName string,
	orderId int64, req *order_request.OrderAddFoodRequest) (int64, []*model.OrderDetailModel, error) {

	// 根据订单ID获取订单信息。
	order, err := serv.GetCloudOrderByID(ctx, orderId, merchantNo)
	if err != nil {
		// 如果订单不存在，返回错误。
		return 0, nil, errors.BadRequest("", "OrderNotExist")
	}
	return serv.AddFoodsByOrder(ctx, order, merchantNo, userID, userName, req)
}

func (serv *OrderCloudService) AddFoodsByOrder(
	ctx context.Context, order *model.CloudOrderModel,
	merchantNo string, userID int64, userName string,
	req *order_request.OrderAddFoodRequest,
) (int64, []*model.OrderDetailModel, error) {

	if order.State != 1 && order.State != 2 {
		// 如果订单状态无效，返回错误。
		return 0, nil, errors.BadRequest("", "OrderStateInvalid")
	}
	// 更新订单的备注信息。
	order.Remarks = req.OrderRemark
	// 初始化一个切片，用于存储订单详情。
	var detailsData []*model.OrderDetailModel
	err := serv.Trans.Exec(ctx, func(ctx context.Context) error {
		for _, v := range req.OrderDetails {
			// 创建新的食物订单详情。
			food, err := serv.NewFood(ctx, order, merchantNo, userID, userName, v)
			if err != nil {
				// 如果创建失败，返回错误。
				return err
			}
			// 添加食物到订单详情中。
			err = util.GetDB(ctx, serv.DB).Create(food).Error
			if err != nil {
				// 如果添加失败，返回错误。
				return err
			}
			// 将食物信息添加到详情切片中。
			detailsData = append(detailsData, food)
		}
		// 如果订单状态为1，将其更新为2。
		if order.State == 1 {
			order.State = 2
		}
		// 更新订单信息。
		return serv.UpdateOrderData(ctx, order, merchantNo)
	})
	if err != nil {
		return 0, nil, errors.BadRequest("", "AddFoodFailed")
	}
	// 返回桌号和订单详情。
	return order.TableID, detailsData, nil
}

func (serv *OrderCloudService) NewFood(ctx context.Context, order *model.CloudOrderModel, merchantNo string, userID int64, userName string, reqOrderDetail *order_request.OrderDetail) (*model.OrderDetailModel, error) {
	db := util.GetDB(ctx, serv.DB)

	food := model.FoodModel{}
	err := db.Model(food).Where("id = ? and merchant_no = ?", reqOrderDetail.FoodID, merchantNo).Find(&food).Error
	if err != nil {
		// 如果食物不存在，返回错误。
		return nil, errors.BadRequest("", "FoodNotFound")
	}

	if food.CellClearState {
		food.RemainingCount = util.SubtractFloat(food.RemainingCount, reqOrderDetail.FoodsCount)
		if food.RemainingCount < 0 {
			food.RemainingCount = 0
		}
		db.Select("remaining_count").Save(&food)
	}

	// 创建订单详情对象。
	od := &model.OrderDetailModel{
		ID:            0,
		MerchantNo:    merchantNo,
		OrderNo:       order.No,
		FoodID:        reqOrderDetail.FoodID,
		FoodsCount:    reqOrderDetail.FoodsCount,
		CostPrice:     food.CostPrice,
		OriginalPrice: food.Price,
		VipPrice:      food.VipPrice,
		Price:         food.Price,
		TotalPrice:    util.Round(util.MultiplyFloat(reqOrderDetail.FoodsCount, food.Price)),
		IsPrint:       false,
		UserID:        userID,
		State:         2,
		Remarks:       &reqOrderDetail.Remarks,
		IsCombo:       food.IsCombo,
		ComboInfo:     reqOrderDetail.ComboInfo,
		IsSync:        false,
	}
	// 扫码点菜不需要保存OrderId, 普通订单需要
	if !order.IsScanOrder {
		od.OrderID = order.ID
	}
	od.Food = &food

	// 返回订单详情对象。
	return od, nil
}

// V3版本方法 - 支持规格、做法、加料和餐盒

// AddFoodsV3 v3版本加菜方法，支持规格、做法、加料和餐盒
func (serv *OrderCloudService) AddFoodsV3(
	ctx context.Context, merchantNo string, userID int64, userName string,
	orderId int64, req *order_request.OrderAddFoodRequestV3) (int64, []*model.OrderDetailModel, error) {

	// 根据订单ID获取订单信息。
	order, err := serv.GetCloudOrderByID(ctx, orderId, merchantNo)
	if err != nil {
		// 如果订单不存在，返回错误。
		return 0, nil, errors.BadRequest("", "OrderNotExist")
	}
	return serv.AddFoodsByOrderV3(ctx, order, merchantNo, userID, userName, req)
}

// AddFoodsByOrderV3 v3版本按订单加菜方法
func (serv *OrderCloudService) AddFoodsByOrderV3(
	ctx context.Context, order *model.CloudOrderModel,
	merchantNo string, userID int64, userName string,
	req *order_request.OrderAddFoodRequestV3,
) (int64, []*model.OrderDetailModel, error) {

	if order.State != consts.ORDER_STATE_NEW && order.State != consts.ORDER_STATE_UNPAID {
		// 如果订单状态无效，返回错误。
		return 0, nil, errors.BadRequest("", "OrderStateInvalid")
	}
	// 更新订单的备注信息。
	order.Remarks = req.OrderRemark
	// 初始化一个切片，用于存储订单详情。
	var detailsData []*model.OrderDetailModel
	err := serv.Trans.Exec(ctx, func(ctx context.Context) error {
		for _, v := range req.OrderDetails {
			// 第一步：创建主订单详情（规格级别）
			mainDetails, err := serv.NewFoodV3(ctx, order, merchantNo, userID, userName, v, 0) // 传入parentID=0表示顶级
			if err != nil {
				return err
			}

			// 保存主订单详情并获取ID
			var mainDetailID int64
			for _, mainDetail := range mainDetails {
				err = util.GetDB(ctx, serv.DB).Create(mainDetail).Error
				if err != nil {
					return err
				}
				mainDetailID = mainDetail.ID // 获取主订单详情的ID
				detailsData = append(detailsData, mainDetail)
			}

			// 第二步：创建子级详情（加料、做法、餐盒）并设置Pid关联
			// 注意：createAllSubOrderDetails 方法内部已经处理了保存逻辑
			subDetails, err := serv.createAllSubOrderDetails(ctx, order, merchantNo, userID, v, mainDetailID)
			if err != nil {
				return err
			}

			// 将子级详情添加到返回数据中
			detailsData = append(detailsData, subDetails...)
		}
		// 如果订单状态为1，将其更新为2。
		if order.State == 1 {
			order.State = 2
		}
		// 更新订单信息。
		return serv.UpdateOrderData(ctx, order, merchantNo)
	})
	if err != nil {
		return 0, nil, errors.BadRequest("", "AddFoodFailed")
	}
	// 返回桌号和订单详情。
	return order.TableID, detailsData, nil
}

// NewFoodV3 v3版本创建食物订单详情，支持规格、做法、加料和餐盒
func (serv *OrderCloudService) NewFoodV3(ctx context.Context, order *model.CloudOrderModel, merchantNo string, userID int64, userName string, reqOrderDetail *order_request.OrderDetailV3, parentID int64) ([]*model.OrderDetailModel, error) {
	db := util.GetDB(ctx, serv.DB)
	var allOrderDetails []*model.OrderDetailModel

	// 获取基础食物信息（父级菜品）
	parentFood := model.FoodModel{}
	err := db.Model(parentFood).Where("id = ? and merchant_no = ?", reqOrderDetail.FoodID, merchantNo).Find(&parentFood).Error
	if err != nil {
		return nil, errors.BadRequest("", "FoodNotFound")
	}

	// 获取规格食物信息
	var specFood model.FoodModel
	if reqOrderDetail.SpecFoodID != 0 && reqOrderDetail.SpecFoodID != reqOrderDetail.FoodID {
		// 有具体规格
		err = db.Model(specFood).Where("id = ? and merchant_no = ? and pid = ?", reqOrderDetail.SpecFoodID, merchantNo, reqOrderDetail.FoodID).Find(&specFood).Error
		if err != nil {
			return nil, errors.BadRequest("", "FoodSpecNotFound")
		}
	} else {
		// 没有规格，使用父级菜品作为默认规格
		specFood = parentFood
		reqOrderDetail.SpecFoodID = reqOrderDetail.FoodID
	}

	// 处理库存清理
	if specFood.CellClearState {
		specFood.RemainingCount = util.SubtractFloat(specFood.RemainingCount, reqOrderDetail.FoodsCount)
		if specFood.RemainingCount < 0 {
			specFood.RemainingCount = 0
		}
		db.Select("remaining_count").Save(&specFood)
	}

	// 创建主订单详情（规格级别）
	mainOrderDetail := &model.OrderDetailModel{
		ID:            0,
		MerchantNo:    merchantNo,
		OrderNo:       order.No,
		Pid:           parentID, // 设置父级ID
		FoodID:        reqOrderDetail.FoodID,
		SpecFoodID:    reqOrderDetail.SpecFoodID,
		Type:          model.FoodTypeFood, // 主菜品类型
		FoodsCount:    reqOrderDetail.FoodsCount,
		CostPrice:     specFood.CostPrice,
		OriginalPrice: specFood.Price,
		VipPrice:      specFood.VipPrice,
		Price:         specFood.Price,
		TotalPrice:    util.Round(util.MultiplyFloat(reqOrderDetail.FoodsCount, specFood.Price)),
		IsPrint:       false,
		UserID:        userID,
		State:         2,
		Remarks:       &reqOrderDetail.Remarks,
		IsCombo:       specFood.IsCombo,
		ComboInfo:     reqOrderDetail.ComboInfo,
		IsSync:        false,
	}

	// 扫码点菜不需要保存OrderId, 普通订单需要
	if !order.IsScanOrder {
		mainOrderDetail.OrderID = order.ID
	}
	mainOrderDetail.Food = &specFood

	allOrderDetails = append(allOrderDetails, mainOrderDetail)

	return allOrderDetails, nil
}

// createAllSubOrderDetails 创建所有子级订单详情（加料、做法、餐盒）
func (serv *OrderCloudService) createAllSubOrderDetails(ctx context.Context, order *model.CloudOrderModel, merchantNo string, userID int64, reqOrderDetail *order_request.OrderDetailV3, parentID int64) ([]*model.OrderDetailModel, error) {
	var allSubDetails []*model.OrderDetailModel
	db := util.GetDB(ctx, serv.DB)

	// 第一步：处理加料（作为子级）
	var additionDetails []*model.OrderDetailModel
	if reqOrderDetail.Additions != nil && len(reqOrderDetail.Additions) > 0 {
		for _, addition := range reqOrderDetail.Additions {
			additionDetail, err := serv.createSingleSubOrderDetail(ctx, order, merchantNo, userID, addition, model.FoodTypeAddition, parentID)
			if err != nil {
				return nil, err
			}
			// 保存加料详情以获取ID
			err = db.Create(additionDetail).Error
			if err != nil {
				return nil, err
			}
			additionDetails = append(additionDetails, additionDetail)
			allSubDetails = append(allSubDetails, additionDetail)

			// 递归处理加料的子级
			subSubDetails, err := serv.createAllSubOrderDetails(ctx, order, merchantNo, userID, addition, additionDetail.ID)
			if err != nil {
				return nil, err
			}
			allSubDetails = append(allSubDetails, subSubDetails...)
		}
	}

	// 第二步：处理做法（作为子级）
	if reqOrderDetail.Methods != nil && len(reqOrderDetail.Methods) > 0 {
		for _, method := range reqOrderDetail.Methods {
			methodDetail, err := serv.createMethodOrderDetailWithPid(ctx, order, merchantNo, userID, method, parentID)
			if err != nil {
				return nil, err
			}
			// 保存做法详情
			err = db.Create(methodDetail).Error
			if err != nil {
				return nil, err
			}
			allSubDetails = append(allSubDetails, methodDetail)
		}
	}

	// 第三步：处理餐盒（作为子级）
	var lunchBoxDetails []*model.OrderDetailModel
	if reqOrderDetail.LunchBoxes != nil && len(reqOrderDetail.LunchBoxes) > 0 {
		for _, lunchBox := range reqOrderDetail.LunchBoxes {
			lunchBoxDetail, err := serv.createSingleSubOrderDetail(ctx, order, merchantNo, userID, lunchBox, model.FoodTypeLunchBox, parentID)
			if err != nil {
				return nil, err
			}
			// 保存餐盒详情以获取ID
			err = db.Create(lunchBoxDetail).Error
			if err != nil {
				return nil, err
			}
			lunchBoxDetails = append(lunchBoxDetails, lunchBoxDetail)
			allSubDetails = append(allSubDetails, lunchBoxDetail)

			// 递归处理餐盒的子级
			subSubDetails, err := serv.createAllSubOrderDetails(ctx, order, merchantNo, userID, lunchBox, lunchBoxDetail.ID)
			if err != nil {
				return nil, err
			}
			allSubDetails = append(allSubDetails, subSubDetails...)
		}
	}

	return allSubDetails, nil
}

// createSingleSubOrderDetail 创建单个子级订单详情（不处理递归）
func (serv *OrderCloudService) createSingleSubOrderDetail(ctx context.Context, order *model.CloudOrderModel, merchantNo string, userID int64, subDetail *order_request.OrderDetailV3, foodType int64, parentID int64) (*model.OrderDetailModel, error) {
	db := util.GetDB(ctx, serv.DB)

	// 获取子级食物信息
	var subFood model.FoodModel
	err := db.Model(subFood).Where("id = ? and merchant_no = ? and type = ?", subDetail.FoodID, merchantNo, foodType).Find(&subFood).Error
	if err != nil {
		if foodType == model.FoodTypeAddition {
			return nil, errors.BadRequest("", "FoodAdditionNotFound")
		} else {
			return nil, errors.BadRequest("", "LunchBoxNotFound")
		}
	}

	// 创建子级订单详情
	subOrderDetail := &model.OrderDetailModel{
		ID:            0,
		MerchantNo:    merchantNo,
		OrderNo:       order.No,
		Pid:           parentID, // 设置父级ID关联
		FoodID:        subDetail.FoodID,
		Type:          int(foodType),
		FoodsCount:    subDetail.FoodsCount,
		CostPrice:     subFood.CostPrice,
		OriginalPrice: subFood.Price,
		VipPrice:      subFood.VipPrice,
		Price:         subFood.Price,
		TotalPrice:    util.Round(util.MultiplyFloat(subDetail.FoodsCount, subFood.Price)),
		IsPrint:       false,
		UserID:        userID,
		State:         2,
		Remarks:       &subDetail.Remarks,
		IsCombo:       false,
		IsSync:        false,
	}

	// 扫码点菜不需要保存OrderId, 普通订单需要
	if !order.IsScanOrder {
		subOrderDetail.OrderID = order.ID
	}
	subOrderDetail.Food = &subFood

	return subOrderDetail, nil
}

// createMethodOrderDetailWithPid 创建做法订单详情并设置Pid关联
func (serv *OrderCloudService) createMethodOrderDetailWithPid(ctx context.Context, order *model.CloudOrderModel, merchantNo string, userID int64, method *order_request.OrderDetailMethodV3, parentID int64) (*model.OrderDetailModel, error) {
	db := util.GetDB(ctx, serv.DB)

	// 验证做法是否存在
	var methodModel model.MethodModel
	err := db.Where("id = ? AND merchant_no = ? AND state = ?", method.MethodID, merchantNo, model.MethodStateNormal).First(&methodModel).Error
	if err != nil {
		return nil, errors.BadRequest("", "MethodNotFound")
	}

	// 验证做法分组是否匹配
	if method.GroupID != 0 && methodModel.GroupID != method.GroupID {
		return nil, errors.BadRequest("", "MethodGroupMismatch")
	}

	// 创建做法订单详情
	methodOrderDetail := &model.OrderDetailModel{
		ID:            0,
		MerchantNo:    merchantNo,
		OrderNo:       order.No,
		Pid:           parentID, // 设置父级ID关联
		MethodID:      method.MethodID,
		Type:          model.OrderDetailTypeMethod, // 做法类型
		FoodsCount:    float64(method.Count),
		CostPrice:     0, // 做法通常没有成本价
		OriginalPrice: methodModel.Price,
		VipPrice:      methodModel.Price,
		Price:         methodModel.Price,
		TotalPrice:    util.Round(util.MultiplyFloat(float64(method.Count), methodModel.Price)),
		IsPrint:       false,
		UserID:        userID,
		State:         2,
		IsCombo:       false,
		IsSync:        false,
	}

	// 扫码点菜不需要保存OrderId, 普通订单需要
	if !order.IsScanOrder {
		methodOrderDetail.OrderID = order.ID
	}

	return methodOrderDetail, nil
}

// UpdateOrderData 重新计算订单价格
func (serv *OrderCloudService) UpdateOrderData(ctx context.Context, order *model.CloudOrderModel, merchantNo string) error {
	// 从数据库中获取订单详情。
	details := []*model.OrderDetailModel{}

	errDetails := util.GetDB(ctx, serv.DB).Model(&model.OrderDetailModel{}).
		Where("merchant_no = ? and order_no= ? and state IN ?", merchantNo, order.No, []int{1, 2}).
		Find(&details).Error
	if errDetails != nil {
		return errors.BadRequest("", "GetOrderDetailsFailed")
	}

	// 初始化订单的价格信息。
	order.FoodsCount = 0
	order.CostPrice = 0
	order.OriginalPrice = 0
	order.VipPrice = 0

	// 遍历订单详情，计算订单中的食物总数、成本价、原价和会员价。
	for i := range details {
		order.FoodsCount = util.AddFloat(order.FoodsCount, (details)[i].FoodsCount)
		order.CostPrice = util.AddFloat(order.CostPrice, util.MultiplyFloat((details)[i].CostPrice, (details)[i].FoodsCount))
		order.OriginalPrice = util.AddFloat(order.OriginalPrice, util.MultiplyFloat((details)[i].OriginalPrice, (details)[i].FoodsCount))
		order.VipPrice = util.AddFloat(order.VipPrice, util.MultiplyFloat((details)[i].VipPrice, (details)[i].FoodsCount))
	}

	// 对订单的价格信息进行四舍五入。
	order.IgnorePrice = util.Round(order.IgnorePrice)
	order.CostPrice = util.Round(order.CostPrice)
	order.OriginalPrice = util.Round(order.OriginalPrice)
	order.VipPrice = util.Round(order.VipPrice)
	// 计算订单的最终价格。
	order.Price = util.Round(util.SubtractFloat(order.OriginalPrice, order.IgnorePrice))

	selects := []string{
		"foods_count",
		"cost_price",
		"original_price",
		"vip_price",
		"ignore_price",
		"price",
	}
	if order.Remarks != "" {
		selects = append(selects, "remarks")
	}
	err := util.GetDB(ctx, serv.DB).Select(selects).Updates(order).Error

	// 更新数据库中的订单信息。
	if err != nil {
		return errors.BadRequest("", "OrderUpdateFailed")
	}
	return nil
}

func (serv *OrderCloudService) CancelFood(ctx context.Context, userID int64, userName string, merchantNo string, orderId int64, odID int64, cancelFood *food_request.CancelFood) (int64, []*model.OrderDetailModel, error) {
	// 根据订单ID获取订单信息。
	order, err := serv.GetCloudOrderByID(ctx, int64(orderId), merchantNo)
	if err != nil {
		// 如果订单不存在，返回错误。
		return 0, nil, errors.BadRequest("", "OrderNotExist")
	}

	// 检查订单状态是否允许取消。
	if order.State != 1 && order.State != 2 {
		return 0, nil, errors.BadRequest("", "OrderStateNotAllowCancel")
	}

	// 根据订单详情ID获取订单详情信息。
	od, err := serv.GetCloudOrderDetailByID(ctx, odID, merchantNo)
	if err != nil {
		// 如果订单详情不存在，返回错误信息。
		return 0, nil, errors.BadRequest("", "OrderDetailNotFound")
	}

	// 检查菜品状态是否允许取消。
	if od.State == 3 || od.State == 4 {
		return 0, nil, errors.BadRequest("", "FoodStateNotAllowCancel")
	}

	// 初始化更新后的订单详情列表。
	var list []*model.OrderDetailModel

	// 使用事务执行取消菜品操作，确保数据一致性。
	err = serv.Trans.Exec(ctx, func(ctx context.Context) error {
		db := util.GetDB(ctx, serv.DB)

		// 更新原始订单详情的食品数量，并检查取消数量是否合法
		od.FoodsCount = util.SubtractFloat(od.FoodsCount, cancelFood.FoodsCount)
		od.TotalPrice = util.MultiplyFloat(od.FoodsCount, od.OriginalPrice)

		if od.FoodsCount < 0 {
			return errors.BadRequest("", "CancelQuantityExceed")
		}
		if od.FoodsCount != 0 {
			if err := db.Save(od).Error; err != nil {
				return err
			}
			//生成新的Id
			od.ID = 0
		}

		od.State = 4 // 设置状态为取消
		od.FoodsCount = cancelFood.FoodsCount
		od.TotalPrice = util.MultiplyFloat(cancelFood.FoodsCount, od.OriginalPrice)
		od.UserID = userID
		od.Remarks = cancelFood.Remarks

		// 将新生成的取消订单详情记录保存到数据库
		err := db.Save(od).Error
		if err != nil {
			// 如果取消菜品失败，返回错误信息。
			return errors.BadRequest("", "CancelFoodFailed")
		}
		list = append([]*model.OrderDetailModel{}, od)

		// 更新订单数据。
		return serv.UpdateOrderData(ctx, order, merchantNo)
	})

	if err != nil {
		// 如果事务执行失败，返回错误信息。
		return 0, nil, errors.BadRequest("", "CancelFoodFailed")
	}

	// 返回桌号、更新后的订单详情列表和nil错误。
	return order.TableID, list, nil
}

func (serv *OrderCloudService) CancelAllFoods(ctx context.Context, userID int64, merchantNo string, orderId int64, req *food_request.CancelAllFoodRequest) (int64, []*model.OrderDetailModel, error) {
	// 根据订单ID获取订单信息。
	order, err := serv.GetCloudOrderByID(ctx, orderId, merchantNo)
	if err != nil {
		// 如果订单不存在，返回错误。
		return 0, nil, errors.BadRequest("", "OrderNotExist")
	}

	// 检查订单状态是否允许取消。
	if order.State != 1 && order.State != 2 {
		return 0, nil, errors.BadRequest("", "OrderStateNotAllowCancel")
	}

	// 初始化更新后的订单详情列表。
	var list []*model.OrderDetailModel

	// 使用事务执行取消所有菜品操作，确保数据一致性。
	err = serv.Trans.Exec(ctx, func(ctx context.Context) error {
		db := util.GetDB(ctx, serv.DB)

		// 更新原始订单详情的食品数量，并将状态设置为4
		var details []*model.OrderDetailModel
		errDetails := db.Model(&model.OrderDetailModel{}).
			Where(
				"merchant_no = ? and order_no= ? and state IN ?",
				merchantNo, order.No, []int{consts.ORDER_DETAIL_STATE_NEW, consts.ORDER_DETAIL_STATE_UNPAID},
			).Preload("Food").Find(&details).Error
		if errDetails != nil {
			return errors.BadRequest("", "GetOrderDetailsFailed")
		}

		for _, v := range details {
			v.State = 4 // 设置状态为取消
			v.UserID = userID
			v.Remarks = req.Remarks
			list = append(list, v)
			if err := db.Save(v).Error; err != nil {
				return err
			}
		}

		return db.Model(model.CloudOrderModel{}).Where("id = ?", order.ID).Update("state", consts.ORDER_STATE_CANCEL).Error
	})

	if err != nil {
		// 如果事务执行失败，返回错误信息。
		return 0, nil, errors.BadRequest("", "CancelAllFoodsFailed")
	}

	// 返回桌号、更新后的订单详情列表和nil错误。
	return order.TableID, list, nil
}

func (serv *OrderCloudService) UndoCancelFood(ctx *gin.Context, merchantNo string, userID int64, userName string, orderId int64, odId int64) (int64, []*model.OrderDetailModel, error) {
	// 根据订单ID获取订单信息
	order, err := serv.GetCloudOrderByID(ctx, orderId, merchantNo)
	if err != nil {
		return 0, nil, err
	}
	// 检查订单状态是否允许撤销取消
	if order.State != 1 && order.State != 2 {
		return 0, nil, errors.BadRequest("", "OrderStateNotAllowReverse")
	}

	// 根据订单详情ID获取订单详情
	od, err := serv.GetCloudOrderDetailByID(ctx, odId, merchantNo)
	if err != nil {
		return 0, nil, errors.BadRequest("", "OrderDetailNotFound")
	}
	// 检查订单详情状态是否为已取消
	if od.State != 4 {
		return 0, nil, errors.BadRequest("", "OrderStateNotAllowReverse")
	}

	// 初始化订单详情列表变量
	var list []*model.OrderDetailModel
	// 执行事务以撤销取消食物订单
	err = serv.Trans.Exec(ctx, func(ctx context.Context) error {
		db := util.GetDB(ctx, serv.DB)
		empy := ""
		// 创建新的恢复订单详情记录
		od.UserID = userID
		od.State = 2 // 设置状态为正常
		od.Remarks = &empy

		// 将新生成的恢复订单详情记录保存到数据库
		list = append([]*model.OrderDetailModel{}, od)
		err2 := db.Save(od).Error
		if err2 != nil {
			return errors.BadRequest("", "OrderStateNotAllowReverse")
		}
		// 更新订单数据
		return serv.UpdateOrderData(ctx, order, merchantNo)
	})
	if err != nil {
		return 0, nil, err
	}
	// 返回桌号，更新后的订单详情列表和nil错误
	return order.TableID, list, nil
}

// UpdateOrder 更新订单
func (serv *OrderCloudService) UpdateOrder(ctx context.Context, order *model.CloudOrderModel) error {
	return util.GetDB(ctx, serv.DB).Updates(order).Error
}

// FindOrderDetails 根据订单号和状态查找订单详情
func (serv *OrderCloudService) FindOrderDetails(ctx context.Context, orderNo string, state []int, orderBy ...string) (*[]model.OrderDetailModel, error) {
	var list []model.OrderDetailModel
	tx := util.GetDB(ctx, serv.DB).Where("order_no = ?", orderNo).Where("foods_count != 0")
	if state != nil {
		tx = tx.Where("state IN ?", state)
	}
	if len(orderBy) > 0 {
		tx = tx.Order(orderBy[0])
	}
	err := tx.Find(&list).Error
	return &list, err
}

// HandlePaid 处理已支付订单
func (serv *OrderCloudService) HandlePaid(ctx context.Context, order *model.CloudOrderModel, isVip bool) error {
	type DetailData struct {
		State      int     `json:"state"`
		Price      float64 `json:"price"`
		TotalPrice float64 `json:"total_price"`
	}
	oldPrice := util.AddFloat(order.Price, order.IgnorePrice)
	newPrice := order.Price
	discountPercent := util.DivideFloat(newPrice, oldPrice)
	details, err := serv.FindOrderDetails(ctx, order.No, []int{2}, "created_at DESC,foods_count DESC")
	if err != nil {
		return err
	}
	remainingAmount := order.Price

	for index, detail := range *details {
		detailData := DetailData{
			State: 3, // 设置状态为已支付
		}
		if isVip {
			detailData.Price = util.RoundUp(util.MultiplyFloat(detail.VipPrice, discountPercent))
		} else {
			detailData.Price = util.RoundUp(util.MultiplyFloat(detail.OriginalPrice, discountPercent))
		}
		if index == len(*details)-1 {
			detailData.TotalPrice = remainingAmount
		} else {
			detailData.TotalPrice = util.RoundUp(util.MultiplyFloat(detailData.Price, detail.FoodsCount))
			remainingAmount = util.SubtractFloat(remainingAmount, detailData.TotalPrice)
		}

		if err = util.GetDB(ctx, serv.DB).Model(&model.OrderDetailModel{}).Where("id = ?", detail.ID).Updates(map[string]any{
			"state":       detailData.State,
			"price":       detailData.Price,
			"total_price": detailData.TotalPrice,
		}).Error; err != nil {
			return err
		}
	}

	return nil
}

func (serv *OrderCloudService) SplitOrder(ctx context.Context, orderID int64, userID int64, userName string, merchantNo string, req *order_request.SplitOrder) (*model.CloudOrderModel, error) {
	var newOrder *model.CloudOrderModel
	// 使用事务执行分单操作，确保数据一致性
	return newOrder, serv.Trans.Exec(ctx, func(ctx context.Context) error {
		//获取订单
		order, err := serv.GetCloudOrderByID(ctx, orderID, merchantNo)
		if err != nil {
			return err
		}
		//判断订单状态
		if order.State != 1 && order.State != 2 {
			return errors.BadRequest("", "OrderStateNotAllowReverse")
		}

		// 创建新订单
		newOrder, err = serv.CreateOrder(ctx, merchantNo, userID, userName, req.CustomersCount, order.TableID, "split")

		for _, v := range req.OrderDetails {
			if v.FoodsCount == 0 {
				return errors.BadRequest("", "数量不能小于或等于0")
			}
			od, err := serv.GetCloudOrderDetailByID(ctx, v.ID, merchantNo)
			if err != nil {
				return errors.BadRequest("", "OrderDetails异常")
			}
			if od.FoodsCount < v.FoodsCount {
				return errors.BadRequest("", "分单数量不能大于已点数量")
			}
			if od.State == 3 || od.State == 4 || od.State == 5 {
				return errors.BadRequest("", "OrderStateNotAllowReverse")
			}

			if od.FoodsCount > v.FoodsCount {
				// 更新原订单美食数量
				od.FoodsCount = util.SubtractFloat(od.FoodsCount, v.FoodsCount)
				od.TotalPrice = util.MultiplyFloat(od.FoodsCount, od.OriginalPrice)
				//更新原来的订单美食
				util.GetDB(ctx, serv.DB).Save(od)
				//创建新订单美食
				od.ID = 0
			}
			od.OrderID = newOrder.ID
			od.OrderNo = newOrder.No
			od.FoodsCount = v.FoodsCount
			od.TotalPrice = util.MultiplyFloat(od.FoodsCount, od.OriginalPrice)
			od.UserID = userID

			util.GetDB(ctx, serv.DB).Save(od)

		}
		// 更新新订单和原订单的数据
		if errUpdate := serv.UpdateOrderData(ctx, newOrder, merchantNo); errUpdate != nil {
			return errUpdate
		}
		return serv.UpdateOrderData(ctx, order, merchantNo)
	})
}

// UpdateIgnorePrice 更新订单的抹零金额
func (serv *OrderCloudService) UpdateIgnorePrice(ctx context.Context, orderID int64, merchantNo string, ignorePrice float64) error {
	return util.GetDB(ctx, serv.DB).
		Model(&model.CloudOrderModel{}).
		Where("merchant_no = ? and id = ?", merchantNo, orderID).
		Update("ignore_price", ignorePrice).Error
}

// CreateScanOrder 创建扫码点餐订单
func (serv *OrderCloudService) CreateScanOrder(ctx context.Context, formItem scan_request.ScanOrderCreateRequest) (*model.CloudOrderModel, error) {

	OrderNo, err := serv.getOrderNo(ctx)
	if err != nil {
		return nil, errors.BadRequest("", "CreateOrderFailed")
	}
	createOrder := &model.CloudOrderModel{
		MerchantNo:     formItem.MerchantNo,
		No:             OrderNo,
		TableID:        formItem.TableID,
		OpenID:         formItem.OpenID,
		CustomersCount: formItem.CustomersCount,
		WechatUserID:   formItem.WechatUserID,
		State:          2,
		Remarks:        *formItem.Remark,
		IsScanOrder:    true,
	}
	err = serv.Trans.Exec(ctx, func(ctx context.Context) error {
		err := util.GetDB(ctx, serv.DB).Create(&createOrder).Error
		if err != nil {
			return err
		}
		details := &order_request.OrderAddFoodRequest{OrderDetails: formItem.OrderDetails, OrderRemark: *formItem.Remark}
		_, _, err = serv.AddFoodsByOrder(ctx, createOrder, formItem.MerchantNo, 0, "", details)
		if err != nil {
			return err
		}

		return nil
	})
	if err != nil {
		return nil, err
	}
	return createOrder, nil
}

// FinishScanOrder 完成扫码点餐订单
func (serv *OrderCloudService) FinishScanOrder(ctx context.Context, orderNo string, merchantNo string, paidAt time.Time) (*model.CloudOrderModel, error) {

	order, err := serv.GetCloudOrderByNo(ctx, orderNo, merchantNo)
	if err != nil {
		return nil, err
	}
	if order == nil {
		logging.Context(ctx).Error(
			"FinishScanOrder 订单不存在",
			zap.String("orderNo", orderNo),
			zap.String("merchantNo", merchantNo),
			zap.Error(err),
		)
		return nil, nil
	}
	// 如果不是扫码点菜则跳过
	if !order.IsScanOrder {
		return nil, nil
	}
	// 如果已结账则跳过
	if order.State == 3 {
		return nil, nil
	}
	if order.State != 2 {
		return nil, errors.BadRequest("", "OrderStateNotAllowFinish")
	}
	err = serv.Trans.Exec(ctx, func(ctx context.Context) error {
		// 更新订单状态
		err = util.GetDB(ctx, serv.DB).Model(&model.CloudOrderModel{}).
			Where("id = ?", order.ID).
			Updates(map[string]any{"state": consts.ORDER_STATE_PAID, "paid_at": paidAt}).Error
		if err != nil {
			return err
		}
		// 更新订单详情状态
		return util.GetDB(ctx, serv.DB).Model(&model.OrderDetailModel{}).
			Where("order_no = ? and state = ?", orderNo, consts.ORDER_DETAIL_STATE_UNPAID).
			Update("state", consts.ORDER_DETAIL_STATE_PAID).Error
	})
	return order, err
}

func (serv *OrderCloudService) CollageOrder(ctx context.Context, userID int64, userName string, orderID int64, targetOrderID int64, merchantNo string) (*model.TableModel, *model.TableModel, *model.CloudOrderModel, *model.CloudOrderModel, error) {

	var order *model.CloudOrderModel
	var targetOrder *model.CloudOrderModel
	// 使用事务处理订单合并过程，确保数据一致性
	err := serv.Trans.Exec(ctx, func(ctx context.Context) error {
		var err error
		// 获取原始订单信息
		order, err = serv.GetCloudOrderByID(ctx, orderID, merchantNo)
		if err != nil {
			return errors.BadRequest("", "OrderNotExist")
		}
		// 检查原始订单状态是否允许合并
		if order.State != 1 && order.State != 2 {
			return errors.BadRequest("", "OrderStateNotAllowCancel")
		}
		// 获取目标订单信息
		targetOrder, err = serv.GetCloudOrderByID(ctx, targetOrderID, merchantNo)
		if err != nil {
			return errors.BadRequest("", "OrderNotExist")
		}
		// 检查目标订单状态是否允许合并
		if targetOrder.State != 1 && targetOrder.State != 2 {
			return errors.BadRequest("", "OrderStateNotAllowCancel")
		}

		order.State = 5
		order.User = userName

		if err = util.GetDB(ctx, serv.DB).Save(order).Error; err != nil {
			return errors.BadRequest("", "OrderStateNotAllowReverse")
		}
		// 获取原始订单的详情
		orderDetails, err := serv.GetCloudOrderDetailByOrderNo(ctx, order.No, []int{1, 2}, merchantNo)
		if err != nil {
			return errors.BadRequest("", "OrderDetailNotFound")
		}

		var ids []int64
		// 获取需要合并的订单详情记录ID列表
		for _, data := range orderDetails {
			ids = append(ids, data.ID)
		}

		// 更新这些订单详情记录的状态为6（合并状态），并更新更新时间
		err = util.GetDB(ctx, serv.DB).
			Model(&model.OrderDetailModel{}).
			Where("id IN ?", ids).
			Updates(map[string]interface{}{
				"order_no": targetOrder.No,
			}).Error
		if err != nil {
			return errors.BadRequest("", "OrderStateNotAllowReverse")
		}
		// 更新目标订单的数据
		return serv.UpdateOrderData(ctx, targetOrder, merchantNo)
	})
	if err != nil {
		return nil, nil, nil, nil, err
	}

	// 获取原始订单对应的餐桌信息
	table := model.TableModel{}
	err = util.GetDB(ctx, serv.DB).Where("id = ? and merchant_no = ?", order.TableID, merchantNo).First(&table).Error
	if err != nil {
		return nil, nil, nil, nil, errors.BadRequest("", "原始餐桌不存在")
	}

	// 获取目标订单对应的餐桌信息
	targetTable := model.TableModel{}
	err = util.GetDB(ctx, serv.DB).Where("id = ? and merchant_no = ?", targetOrder.TableID, merchantNo).First(&targetTable).Error
	if err != nil {
		return nil, nil, nil, nil, errors.BadRequest("", "目标餐桌不存在")
	}

	// 返回合并后的订单和餐桌信息
	return &table, &targetTable, order, targetOrder, nil

}

// ChangeTable 修改订单桌号
func (serv *OrderCloudService) ChangeTable(ctx context.Context, orderID int64, merchantNo string, tableID int64) error {
	return util.GetDB(ctx, serv.DB).Model(&model.CloudOrderModel{}).
		Where("merchant_no = ? and id = ?", merchantNo, orderID).
		Update("table_id", tableID).Error
}

package food_resource

import (
	"context"
	"ros-api-go/internal/config"
	"ros-api-go/internal/model"
	"ros-api-go/pkg/i18n"
	"ros-api-go/pkg/util"
)

type FoodResource struct {
	ID               int64    `json:"id"`                 // Unique ID
	MerchantNo       string   `json:"merchant_no"`        // 商家编号
	Pid              int64    `json:"pid"`                // 父ID
	FoodCategoryID   int64    `json:"food_category_id"`   // 分类ID
	FoodCategoryName string   `json:"food_category_name"` // 分类名称
	Image            string   `json:"image"`              // 图片
	ShortcutCode     string   `json:"shortcut_code"`      // 快捷码
	Name             string   `json:"name"`               // 名称
	NameUg           string   `json:"name_ug"`            // 名称(维语)
	NameZh           string   `json:"name_zh"`            // 名称(中文)
	CostPrice        float64  `json:"cost_price"`         // 定价
	VipPrice         float64  `json:"vip_price"`          // 会员价
	Price            float64  `json:"price"`              // 现价
	FormatId         int      `json:"format_id"`          // 规格ID
	IsSpecialFood    bool     `json:"is_special_food"`    // 是否特色菜
	SupportScanOrder bool     `json:"support_scan_order"` // 是否支持扫码点单
	CellClearState   bool     `json:"cell_clear_state"`   // 否设置剩余数量(1表示设置剩余数量、0表示没设置)
	SellClearCount   float64  `json:"sell_clear_count"`   // 美食剩余限量数
	RemainingCount   float64  `json:"remaining_count"`    // 美食剩余数量
	IsCombo          bool     `json:"is_combo_food"`      // 是否套餐菜
	Sort             int64    `json:"sort"`               // 排序
	State            int64    `json:"state"`              // 状态
	IsSync           bool     `json:"is_sync"`            // 是否同步
	SpecID           int64    `json:"spec_id"`            // 规格ID
	Type             int64    `json:"type"`               // 类型
	CreatedAt        *string  `json:"created_at"`         // Create time
	UpdatedAt        *string  `json:"updated_at"`         // Update time
	DeletedAt        *string  `json:"deleted_at"`         // Delete time
	SpecsCount       int      `json:"specs_count"`        // 规格数量
	Combos           []*Combo `json:"combos" gorm:"foreignkey:LunchBoxID;references:ID"`
}

type Combo struct {
	ID         int64   `json:"id"`
	ComboID    int64   `json:"combo_id"`
	FoodID     int64   `json:"food_id" `
	ParentID   int64   `json:"parent_id"`
	Type       int64   `json:"type"`
	ComboPrice float64 `json:"combo_price"`
	NameUg     string  `json:"name_ug"`
	NameZh     string  `json:"name_zh"`
	Count      int     `json:"count"`
	Childs     []Combo `json:"childs" gorm:"foreignkey:ParentID;references:ID"`
}

func (rc *FoodResource) Make(ctx *context.Context, item *model.FoodModel) *FoodResource {
	if item == nil {
		return nil
	}
	data := FoodResource{
		ID:               item.ID,
		MerchantNo:       item.MerchantNo,
		Pid:              item.Pid,
		FoodCategoryID:   item.FoodCategoryID,
		Image:            config.C.Storage.OSS.Endpoint + item.Image,
		ShortcutCode:     item.ShortcutCode,
		NameUg:           item.NameUg,
		NameZh:           item.NameZh,
		CostPrice:        item.CostPrice,
		VipPrice:         item.VipPrice,
		Price:            item.Price,
		FormatId:         item.FormatID,
		IsSpecialFood:    item.IsSpecialFood,
		SupportScanOrder: item.SupportScanOrder,
		CellClearState:   item.CellClearState,
		SellClearCount:   item.SellClearCount,
		RemainingCount:   item.RemainingCount,
		IsCombo:          item.IsCombo,
		Sort:             item.Sort,
		State:            item.State,
		IsSync:           item.IsSync,
		Type:             item.Type,
		SpecID:           item.SpecID,
		SpecsCount:       item.SpecsCount,
		CreatedAt:        util.FormatDateTime(&item.CreatedAt),
		UpdatedAt:        util.FormatDateTime(&item.UpdatedAt),
		DeletedAt:        util.FormatDateTime(item.DeletedAt),
	}

	isZh := i18n.IsZh(ctx)

	data.Name = data.NameUg
	if isZh {
		data.Name = data.NameZh
	}

	if item.FoodCategory != nil {
		data.FoodCategoryName = item.FoodCategory.NameUg
		if isZh {
			data.FoodCategoryName = item.FoodCategory.NameZh
		}
	}

	if item.Combos != nil {
		combos := make([]*Combo, len(item.Combos))
		for i, combo := range item.Combos {
			combos[i] = &Combo{
				ID:         combo.ID,
				ComboID:    combo.ComboID,
				FoodID:     combo.FoodID,
				Type:       util.StrToInt64(combo.Type),
				ComboPrice: combo.ComboPrice,
				NameUg:     combo.NameUg,
				NameZh:     combo.NameZh,
				Count:      combo.Count,
			}
			if combo.Childs != nil {
				childs := make([]Combo, len(combo.Childs))
				for j, child := range combo.Childs {
					childs[j] = Combo{
						ID:         child.ID,
						ComboID:    child.ComboID,
						FoodID:     child.FoodID,
						Type:       util.StrToInt64(child.Type),
						ParentID:   child.ParentID,
						ComboPrice: child.ComboPrice,
						NameUg:     child.NameUg,
						NameZh:     child.NameZh,
						Count:      child.Count,
					}
				}
				combos[i].Childs = childs
			}
		}
		data.Combos = combos
	} else {
		data.Combos = []*Combo{}
	}

	return &data
}

func (rc *FoodResource) Collection(ctx *context.Context, items []*model.FoodModel) []*FoodResource {

	if items == nil || len(items) == 0 {
		return []*FoodResource{}
	}
	data := make([]*FoodResource, len(items))
	for i, food := range items {
		data[i] = rc.Make(ctx, food)
	}
	return data
}

// ParentFoodsForAssociationResource 用于规格关联菜品的资源模型
type ParentFoodsForAssociationResource struct {
	ID               int64   `json:"id"`
	MerchantNo       string  `json:"merchant_no"`
	Pid              int64   `json:"pid"`
	NameUg           string  `json:"name_ug"`
	NameZh           string  `json:"name_zh"`
	FoodCategoryID   int64   `json:"food_category_id"`
	CategoryName     string  `json:"category_name"`
	CategoryNameZh   string  `json:"category_name_zh"` // 分类名称(中文)
	CategoryNameUg   string  `json:"category_name_ug"` // 分类名称(维语)
	ShortcutCode     string  `json:"shortcut_code"`
	CostPrice        float64 `json:"cost_price"`
	VipPrice         float64 `json:"vip_price"`
	Price            float64 `json:"price"`
	FormatID         int     `json:"format_id"`
	SupportScanOrder bool    `json:"support_scan_order"`
	IsComboFood      bool    `json:"is_combo_food"`
	Sort             int64   `json:"sort"`
	State            int64   `json:"state"`
	CreatedAt        *string `json:"created_at"`
	UpdatedAt        *string `json:"updated_at"`
}

func (rc *ParentFoodsForAssociationResource) Make(ctx *context.Context, item *model.FoodModel) *ParentFoodsForAssociationResource {
	if item == nil {
		return nil
	}

	data := ParentFoodsForAssociationResource{
		ID:               item.ID,
		Pid:              item.ID,
		MerchantNo:       item.MerchantNo,
		FoodCategoryID:   item.FoodCategoryID,
		ShortcutCode:     item.ShortcutCode,
		NameUg:           item.NameUg,
		NameZh:           item.NameZh,
		CostPrice:        item.CostPrice,
		VipPrice:         item.VipPrice,
		Price:            item.Price,
		FormatID:         item.FormatID,
		SupportScanOrder: item.SupportScanOrder,
		Sort:             item.Sort,
		State:            item.State,
		CreatedAt:        util.FormatDateTime(&item.CreatedAt),
		UpdatedAt:        util.FormatDateTime(&item.UpdatedAt),
	}

	isZh := i18n.IsZh(ctx)

	if item.FoodCategory != nil {
		data.CategoryNameZh = item.FoodCategory.NameZh
		data.CategoryNameUg = item.FoodCategory.NameUg
		if isZh {
			data.CategoryName = item.FoodCategory.NameZh
		} else {
			data.CategoryName = item.FoodCategory.NameUg
		}
	}

	return &data
}

func (rc *ParentFoodsForAssociationResource) Collection(ctx *context.Context, items []*model.FoodModel) []*ParentFoodsForAssociationResource {

	if items == nil || len(items) == 0 {
		return []*ParentFoodsForAssociationResource{}
	}
	data := make([]*ParentFoodsForAssociationResource, len(items))
	for i, item := range items {
		data[i] = rc.Make(ctx, item)
	}
	return data
}

// SpecFoodsForAssociationResource 用于规格关联菜品的资源模型
type SpecFoodsForAssociationResource struct {
	ID               int64   `json:"id"`
	MerchantNo       string  `json:"merchant_no"`
	Pid              int64   `json:"pid"`
	NameUg           string  `json:"name_ug"`
	NameZh           string  `json:"name_zh"`
	Image            string  `json:"image"`
	SpecNameUg       string  `json:"spec_name_ug"`
	SpecNameZh       string  `json:"spec_name_zh"`
	FoodCategoryID   int64   `json:"food_category_id"`
	CategoryNameUg   string  `json:"category_name_ug"`
	CategoryNameZh   string  `json:"category_name_zh"`
	ShortcutCode     string  `json:"shortcut_code"`
	CostPrice        float64 `json:"cost_price"`
	VipPrice         float64 `json:"vip_price"`
	Price            float64 `json:"price"`
	FormatID         int     `json:"format_id"`
	SupportScanOrder bool    `json:"support_scan_order"`
	IsComboFood      bool    `json:"is_combo_food"`
	CellClearState   bool    `json:"cell_clear_state"`
	SellClearCount   float64 `json:"sell_clear_count"`
	RemainingCount   float64 `json:"remaining_count"`
	Sort             int64   `json:"sort"`
	State            int64   `json:"state"`
	CreatedAt        *string `json:"created_at"`
	UpdatedAt        *string `json:"updated_at"`
}

func (rc *SpecFoodsForAssociationResource) Make(ctx *context.Context, item *model.FoodModel, parentFood *model.FoodModel) *SpecFoodsForAssociationResource {
	if item == nil {
		return nil
	}

	data := SpecFoodsForAssociationResource{
		ID:               item.ID,
		Pid:              item.ID,
		MerchantNo:       item.MerchantNo,
		FoodCategoryID:   item.FoodCategoryID,
		ShortcutCode:     item.ShortcutCode,
		Image:            config.C.Storage.OSS.Endpoint + item.Image,
		SpecNameUg:       item.NameUg,
		SpecNameZh:       item.NameZh,
		CostPrice:        item.CostPrice,
		VipPrice:         item.VipPrice,
		Price:            item.Price,
		FormatID:         item.FormatID,
		SupportScanOrder: item.SupportScanOrder,
		CellClearState:   item.CellClearState,
		SellClearCount:   item.SellClearCount,
		RemainingCount:   item.RemainingCount,
		Sort:             item.Sort,
		State:            item.State,
		CreatedAt:        util.FormatDateTime(&item.CreatedAt),
		UpdatedAt:        util.FormatDateTime(&item.UpdatedAt),
	}

	if parentFood != nil {
		data.NameUg = parentFood.NameUg
		data.NameZh = parentFood.NameZh

		if parentFood.FoodCategory != nil {
			data.CategoryNameUg = parentFood.FoodCategory.NameUg
			data.CategoryNameZh = parentFood.FoodCategory.NameZh
		}
	}

	// 如果不是规格菜, 自己就是父菜, 名称改成标准
	if item.SpecID == 0 {
		data.SpecNameUg = "ئۆلچەملىك"
		data.SpecNameZh = "标准"
	}

	return &data
}

package order_request

import (
	"ros-api-go/internal/model"
	"ros-api-go/pkg/util"
)

// OrderDetail 代表订单详情
type OrderDetail struct {
	FoodID     int64                         `json:"food_id"`
	FoodsCount float64                       `json:"foods_count"`
	Remarks    string                        `json:"remarks"`
	ComboInfo  *util.TArray[model.ComboInfo] `json:"combo_info"`
}

type OrderDetailMethod struct {
	MethodID int64 `json:"method_id"`
	GroupID  int64 `json:"group_id"`
	Count    int   `json:"count"`
}

// OrderAddFoodRequest 代表整个请求体
type OrderAddFoodRequest struct {
	OrderDetails []*OrderDetail `json:"order_details"`
	OrderRemark  string         `json:"order_remark"`
}

// V3 扩展结构

// OrderDetailV3 代表v3版本订单详情，扩展支持规格和餐盒
type OrderDetailV3 struct {
	FoodID     int64                         `json:"food_id"`      // 美食ID（父级菜品ID）
	SpecFoodID int64                         `json:"spec_food_id"` // 规格ID（如果没有规格，则与FoodID相同）
	FoodsCount float64                       `json:"foods_count"`
	Remarks    string                        `json:"remarks"`
	ComboInfo  *util.TArray[model.ComboInfo] `json:"combo_info"`
	Additions  []*OrderDetailV3              `json:"additions"`   // 加料（作为子级）
	Methods    []*OrderDetailMethodV3        `json:"methods"`     // 做法（作为子级）
	LunchBoxes []*OrderDetailV3              `json:"lunch_boxes"` // 餐盒（作为子级）
}

type OrderDetailMethodV3 struct {
	MethodID int64 `json:"method_id"`
	GroupID  int64 `json:"group_id"`
	Count    int   `json:"count"`
}

// OrderAddFoodRequestV3 代表v3版本整个请求体
type OrderAddFoodRequestV3 struct {
	OrderDetails []*OrderDetailV3 `json:"order_details"`
	OrderRemark  string           `json:"order_remark"`
}

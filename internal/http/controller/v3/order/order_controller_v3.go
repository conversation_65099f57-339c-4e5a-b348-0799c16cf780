package order

import (
	"github.com/gin-gonic/gin"
	"ros-api-go/internal/broadcast/order_broadcast"
	"ros-api-go/internal/http/request/order_request"
	"ros-api-go/internal/service"
	"ros-api-go/pkg/errors"
	"ros-api-go/pkg/util"
)

type OrderControllerV3 struct {
	OrderService      *service.OrderService
	OrderCloudService *service.OrderCloudService
	PrintService      *service.PrintService
}

// AddFoodsV3 v3版本加菜接口（扩展支持规格、做法、加料和餐盒）
//
// @Tags 订单相关接口
// @Security ApiTokenAuth
// @Summary 加菜 (v3版本，支持规格、做法、加料和餐盒)
// @Success 200 {object} util.ResponseResult{}
// @Failure 401 {object} util.ResponseResult
// @Failure 500 {object} util.ResponseResult
// @Router /api/v3/orders/add-foods-v3/:id [post]
// @Param id path int64 true "订单ID"
// @Param body body order_request.OrderAddFoodRequestV3 true "加菜数据"
// @X-Author ["Merdan"]
// @X-Date ["2025/07/24 10:00"]
// @X-Version ["3.0"]
func (ctrl *OrderControllerV3) AddFoodsV3(c *gin.Context) {
	ctx := c.Request.Context()
	userID := util.FromUserID(ctx)
	userName := util.FromUserName(ctx)
	merchantNo := c.Request.Header.Get("Merchantno")

	// 解析URL参数中的订单ID
	orderId := util.StrToInt64(c.Param("id"))

	// 初始化v3订单加菜请求对象
	orderRequest := order_request.OrderAddFoodRequestV3{}
	// 绑定HTTP请求体到订单加菜请求对象
	if err := util.ParseJSON(c, &orderRequest); err != nil {
		// 如果绑定失败，返回错误响应
		util.ResError(c, errors.BadRequest("", "FailedToParseJson"))
		return
	}
	// 调用v3业务逻辑添加菜品到订单
	tableId, list, err := ctrl.OrderCloudService.AddFoodsV3(ctx, merchantNo, userID, userName, orderId, &orderRequest)
	if err != nil {
		// 如果添加失败，返回错误响应
		util.ResError(c, err)
		return
	}

	// 触发打印任务
	ctrl.PrintService.FoodAction(ctx, merchantNo, "加菜", tableId, orderId, userName, list)

	// 推送广播
	order_broadcast.SendOrderBroadcast(order_broadcast.ActionAddFood, merchantNo, tableId, orderId, nil)
	// 返回成功响应
	util.ResOK(c)
}

// CancelFoodV3 v3版本退菜接口（支持基于Pid关联关系的退菜）
//
// @Tags 订单相关接口
// @Security ApiTokenAuth
// @Summary 退菜 (v3版本，支持基于Pid关联关系的退菜)
// @Success 200 {object} util.ResponseResult{data=order_request.CancelFoodV3Response}
// @Failure 401 {object} util.ResponseResult
// @Failure 500 {object} util.ResponseResult
// @Router /api/v3/orders/cancel-food-v3/:order_id/:od_id [post]
// @Param order_id path int64 true "订单ID"
// @Param od_id path int64 true "订单详情ID"
// @Param body body order_request.CancelFoodV3Request true "退菜数据"
// @X-Author ["Merdan"]
// @X-Date ["2025/07/24 10:00"]
// @X-Version ["3.0"]
func (ctrl *OrderControllerV3) CancelFoodV3(c *gin.Context) {
	ctx := c.Request.Context()
	userID := util.FromUserID(ctx)
	userName := util.FromUserName(ctx)
	merchantNo := c.Request.Header.Get("Merchantno")

	// 解析URL参数
	orderId := util.StrToInt64(c.Param("order_id"))
	odID := util.StrToInt64(c.Param("od_id"))

	if odID == 0 || orderId == 0 {
		util.ResError(c, errors.BadRequest("", "FailedToParseQuery"))
		return
	}

	// 初始化v3退菜请求对象
	cancelRequest := order_request.CancelFoodV3Request{}
	// 绑定HTTP请求体到退菜请求对象
	if err := util.ParseJSON(c, &cancelRequest); err != nil {
		util.ResError(c, errors.BadRequest("", "FailedToParseJson"))
		return
	}

	// 验证订单详情ID是否匹配
	if cancelRequest.OrderDetailID != odID {
		util.ResError(c, errors.BadRequest("", "OrderDetailIDMismatch"))
		return
	}

	// 调用v3业务逻辑退菜
	tableId, cancelledDetails, err := ctrl.OrderCloudService.CancelFoodV3(ctx, userID, userName, merchantNo, orderId, &cancelRequest)
	if err != nil {
		util.ResError(c, err)
		return
	}

	// 触发打印任务
	ctrl.PrintService.FoodAction(ctx, merchantNo, "退菜", tableId, orderId, userName, cancelledDetails)

	// 推送广播
	order_broadcast.SendOrderBroadcast(order_broadcast.ActionCancelFood, merchantNo, tableId, orderId, cancelledDetails)

	// 返回成功响应
	util.ResOK(c)
}

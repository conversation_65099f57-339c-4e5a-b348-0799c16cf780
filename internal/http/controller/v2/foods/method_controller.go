package foods

import (
	"github.com/gin-gonic/gin"
	"ros-api-go/internal/handler"
	"ros-api-go/internal/http/request/food_request"
	"ros-api-go/internal/http/resource/food_resource"
	"ros-api-go/internal/service/food_service"
	"ros-api-go/pkg/util"
)

type MethodController struct {
	MethodService             *food_service.MethodService
	MerchantInfoUpdateHandler *handler.MerchantInfoUpdateHandler
}

// GetMethodList 获取做法列表
//
// @Tags 做法管理
// @Security ApiAuthToken
// @Summary 获取做法列表
// @Param group_id query int false "做法分组ID"
// @Param keyword query string false "搜索关键词"
// @Success 200 {object} util.ResponseResult{Data=[]food_resource.MethodResource}
// @Failure 401 {object} util.ResponseResult
// @Failure 500 {object} util.ResponseResult
// @Router /api/v2/methods [get]
// @X-Author ["Merdan"]
// @X-Date ["2025/07/08 18:00"]
// @X-Version ["2.0"]
func (ctrl *MethodController) GetMethodList(c *gin.Context) {
	ctx := c.Request.Context()
	merchantNo := util.GetMerchantNo(c)

	var req food_request.MethodListRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		util.ResError(c, err)
		return
	}

	methods, err := ctrl.MethodService.GetList(ctx, merchantNo, &req)
	if err != nil {
		util.ResError(c, err)
		return
	}

	util.ResSuccess(c, (&food_resource.MethodResource{}).Collection(methods))
}

// CreateMethod 创建做法
//
// @Tags 做法管理
// @Security ApiAuthToken
// @Summary 创建做法
// @Param request body food_request.CreateMethodRequest true "创建做法请求参数"
// @Produce json
// @Success 200 {object} util.ResponseResult{Data=food_resource.MethodResource}
// @Failure 401 {object} util.ResponseResult
// @Failure 500 {object} util.ResponseResult
// @Router /api/v2/methods [post]
// @X-Author ["Merdan"]
// @X-Date ["2025/07/08 18:00"]
// @X-Version ["2.0"]
func (ctrl *MethodController) CreateMethod(c *gin.Context) {
	ctx := c.Request.Context()
	merchantNo := util.GetMerchantNo(c)

	var req food_request.CreateMethodRequest
	if err := util.ParseJSON(c, &req); err != nil {
		util.ResError(c, err)
		return
	}

	_, err := ctrl.MethodService.Create(ctx, &req, merchantNo)
	if err != nil {
		util.ResError(c, err)
		return
	}

	util.ResOK(c)
	ctrl.MerchantInfoUpdateHandler.UpdateMerchantInfoUpdates(merchantNo, handler.MethodUpdated)
}

// UpdateMethod 更新做法
//
// @Tags 做法管理
// @Security ApiAuthToken
// @Summary 更新做法
// @Param id path int true "做法ID"
// @Param request body food_request.UpdateMethodRequest true "更新做法请求参数"
// @Produce json
// @Success 200 {object} util.ResponseResult{Data=food_resource.MethodResource}
// @Failure 401 {object} util.ResponseResult
// @Failure 500 {object} util.ResponseResult
// @Router /api/v2/methods/{id} [put]
// @X-Author ["Merdan"]
// @X-Date ["2025/07/08 18:00"]
// @X-Version ["2.0"]
func (ctrl *MethodController) UpdateMethod(c *gin.Context) {
	ctx := c.Request.Context()
	merchantNo := util.GetMerchantNo(c)

	id := util.StrToInt64(c.Param("id"))

	var req food_request.UpdateMethodRequest
	if err := util.ParseJSON(c, &req); err != nil {
		util.ResError(c, err)
		return
	}

	_, err := ctrl.MethodService.Update(ctx, id, &req, merchantNo)
	if err != nil {
		util.ResError(c, err)
		return
	}

	util.ResOK(c)
	ctrl.MerchantInfoUpdateHandler.UpdateMerchantInfoUpdates(merchantNo, handler.MethodUpdated)
}

// DeleteMethod 删除做法
//
// @Tags 做法管理
// @Security ApiAuthToken
// @Summary 删除做法
// @Param id path int true "做法ID"
// @Success 200 {object} util.ResponseResult
// @Failure 401 {object} util.ResponseResult
// @Failure 500 {object} util.ResponseResult
// @Router /api/v2/methods/{id} [delete]
// @X-Author ["Merdan"]
// @X-Date ["2025/07/08 18:00"]
// @X-Version ["2.0"]
func (ctrl *MethodController) DeleteMethod(c *gin.Context) {
	ctx := c.Request.Context()
	merchantNo := util.GetMerchantNo(c)

	id := util.StrToInt64(c.Param("id"))

	err := ctrl.MethodService.Delete(ctx, id, merchantNo)
	if err != nil {
		util.ResError(c, err)
		return
	}

	util.ResOK(c, "DeleteSuccess")
	ctrl.MerchantInfoUpdateHandler.UpdateMerchantInfoUpdates(merchantNo, handler.MethodUpdated)
}

// SaveMethodSort 保存做法排序
//
// @Tags 做法管理
// @Security ApiAuthToken
// @Summary 保存做法排序
// @Param request body food_request.SaveMethodSortRequest true "排序请求参数"
// @Produce json
// @Success 200 {object} util.ResponseResult
// @Failure 401 {object} util.ResponseResult
// @Failure 500 {object} util.ResponseResult
// @Router /api/v2/methods/sort [post]
// @X-Author ["Merdan"]
// @X-Date ["2025/07/08 18:00"]
// @X-Version ["2.0"]
func (ctrl *MethodController) SaveMethodSort(c *gin.Context) {
	ctx := c.Request.Context()
	merchantNo := util.GetMerchantNo(c)

	var req food_request.SaveMethodSortRequest
	if err := util.ParseJSON(c, &req); err != nil {
		util.ResError(c, err)
		return
	}

	err := ctrl.MethodService.SaveSort(ctx, &req, merchantNo)
	if err != nil {
		util.ResError(c, err)
		return
	}

	util.ResOK(c, "SortSaveSuccess")
	ctrl.MerchantInfoUpdateHandler.UpdateMerchantInfoUpdates(merchantNo, handler.MethodUpdated)
}

// GetMethodFoods 获取做法关联的美食列表
//
// @Tags 做法管理
// @Security ApiAuthToken
// @Summary 获取做法关联的美食列表
// @Param method_id query int true "做法ID"
// @Param keyword query string false "搜索关键词"
// @Success 200 {object} util.ResponseResult{Data=[]food_resource.MethodFoodResource}
// @Failure 401 {object} util.ResponseResult
// @Failure 500 {object} util.ResponseResult
// @Router /api/v2/methods/foods [get]
// @X-Author ["Merdan"]
// @X-Date ["2025/07/08 18:00"]
// @X-Version ["2.0"]
func (ctrl *MethodController) GetMethodFoods(c *gin.Context) {
	ctx := c.Request.Context()
	merchantNo := util.GetMerchantNo(c)

	var req food_request.MethodFoodListRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		util.ResError(c, err)
		return
	}
	id := util.StrToInt64(c.Param("id"))

	_, foodMethods, err := ctrl.MethodService.GetMethodFoods(ctx, merchantNo, id, &req)
	if err != nil {
		util.ResError(c, err)
		return
	}

	util.ResSuccess(c, (&food_resource.MethodFoodResource{}).Collection(foodMethods))
}

// SaveFoodMethod 保存美食做法关联
//
// @Tags 做法管理
// @Security ApiAuthToken
// @Summary 保存美食做法关联
// @Param request body food_request.SaveFoodMethodRequest true "关联请求参数"
// @Produce json
// @Success 200 {object} util.ResponseResult
// @Failure 401 {object} util.ResponseResult
// @Failure 500 {object} util.ResponseResult
// @Router /api/v2/methods/foods [post]
// @X-Author ["Merdan"]
// @X-Date ["2025/07/08 18:00"]
// @X-Version ["2.0"]
func (ctrl *MethodController) SaveFoodMethod(c *gin.Context) {
	ctx := c.Request.Context()
	merchantNo := util.GetMerchantNo(c)

	var req food_request.SaveFoodMethodRequest
	if err := util.ParseJSON(c, &req); err != nil {
		util.ResError(c, err)
		return
	}

	err := ctrl.MethodService.SaveFoodMethod(ctx, &req, merchantNo)
	if err != nil {
		util.ResError(c, err)
		return
	}

	util.ResOK(c, "SaveSuccess")
	ctrl.MerchantInfoUpdateHandler.UpdateMerchantInfoUpdates(merchantNo, handler.MethodUpdated)
}

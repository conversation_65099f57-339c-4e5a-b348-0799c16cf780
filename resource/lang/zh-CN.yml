# 通用message
Success: "操作成功"
GetSuccess: "获取成功"
GetFailed: "获取失败"
CreateSuccess: "创建成功"
CreateFailed: "创建失败"
UpdateSuccess: "更新成功"
UpdateFailed: "更新失败"
DeleteSuccess: "删除成功"
DeleteFailed: "删除失败"
OperateSuccess: "操作成功"
OperateFailed: "操作失败"
DataNotFound: "数据不存在"
NotFound: "未找到相关资源"
MethodNotAllowed: "不支持的请求方法"
EmptyQueryResult: "未找到符合条件的数据"
ValidationFailed: "数据验证失败"
UserExists: "用户已存在"
NoActiveMerchant: "当前账户没有有效的商户"
MerchantNotFound: "商户信息不存在"
MerchantNotActive: "商户已禁用"
NoActiveMerchantFound: "商户不存在或已禁用，请联系管理员"
MerchantRoleNotFound: "商户角色不存在"
CustomerNotFound: "会员不存在"
CustomerBalanceNotEnough: "会员余额不足"
PasswordNotMatch: "密码与确认密码不一致"
PasswordCannotBeSameAsOriginal: "新密码与原始密码不能相同"
OperationPasswordNotSet: "请先设置操作密码，再进行操作"
InvalidOperationPassword: "操作密码错误"
InvalidClientType: "客户端类型错误"

# 框架部分
FailedToParseJson: "表单参数有误: %v"
FailedToParseQuery: "查询参数有误: %v"
UnsupportedLocale: "不支持的语言环境: %v"
InternalServerError: "服务器内部错误, 请稍后重试"

# 时间
InvalidDateRange: "日期范围无效"
InvalidBeginDateRange: "开始时间格式错误"
InvalidEndDateRange: "结束时间格式错误"

# 业务部分
UserLoggedIn: "用户登录成功"
UserAlreadyLogin: "用户已使用其他设备登录"
NoRightToLoginAsMaster: "您的账户不是主账号，无法登录"
InvalidUsernameOrPassword: "用户名或密码错误"
UserNotFound: "用户信息不存在"
UserNotActive: "用户已禁用，请联系管理员"
InvalidToken: "您还没有登录"
InvalidPhoneNumber: "手机号格式错误"
NoRightToLoginCashier: "对不起，您没有登录收银端的权限"
PermissionDenied: "对不起，您没有权限操作此资源"
FailedToGetPermissions: "获取用户权限失败"
ConnectionFailedToLocalServer: "连接本地服务失败"
FailedToGetLocaleService: "本地服务获取失败"
FailedToRegisterLocalServer: "本地服务注册失败"
FailedToInactiveLocalServer: "本地服务下线失败"
FailedToCreateClientPublishPermission: "注册MQTT客户端发布权限失败"
HasActiveLocalServer: "当前有其他设备注册"
HasLoginAsMainServer: "当前用户已在其他设备作为主机登录"
LocalServerNotFound: "本地服务不存在"
NotEnoughDeviceInfo: "设备信息不足"
FailedToGetCloudAuthInfo: "云端认证信息获取失败"
FailedToGetHandoverLog: "交班记录获取失败"
HasNotSyncedOrders: "您还有未同步的订单，请等待同步完成后再进行操作"
FailedToGenerateToken: "生成Token失败"
LocalServerNotRegistered: "本地服务未注册，请先确保收银机正常运行登录"
MerchantLocalServerNotActive: "商家服务没有启用，暂时无法使用扫码点菜功能"
FailedToGetMerchantData: "商户信息获取失败"
UserNotBindMerchantOrMerchantNotActive: "用户未绑定商户或商户已禁用"
MerchantNotSupportThirdPartPay: "您还没有开通微信或支付宝线上支付通道，请联系销售人员开通"
MerchantNotSupportWechatPay: "您还没有开通微信线上支付通道，请联系销售人员开通"
MerchantNotSupportAlipay: "您还没有开通支付宝线上支付通道，请联系销售人员开通"
PleaseUseAlipay: "商家暂不支持微信支付，请使用支付宝扫码支付"
PleaseUseWechatPay: "商家暂不支持支付宝支付，请使用微信扫码支付"
PaymentNoRequired: "支付编号不能为空"
PaymentExists: "支付编号已存在"
PaymentNotFound: "支付编号不存在"
PaymentNotPaid: "支付编号未支付"
InvalidPayType: "支付方式不合法"
PayAmountInvalid: "支付金额不能小于0.01"
AmountNotMatch: "支付金额与订单金额不匹配"
PaymentReverted: "支付已撤销"
PaymentExpired: "支付编号已过期"
PaymentAlreadyPaid: "订单已支付完成"
PaymentFailed: "支付失败，请稍后再试"
PaymentListFailed: "获取支付列表失败"
ValidateSignFailed: "签名验证失败"
InvalidAuthCode: "请提供正确的微信或支付宝付款码"
InvalidAlipayAuthCode: "请提供正确的支付宝付款码"
InvalidWechatAuthCode: "请提供正确的微信付款码"
InvalidWechatCode: "微信code有误"
PaySuccess: "支付成功"
PaymentQuerySuccess: "支付查询成功"
RefundSuccess: "退款成功"
RefundFailed: "退款失败"
PaymentCannotRevert: "支付状态不支持撤销"
RefundAmountExceed: "退款金额超过支付金额"
NotSupportOfflinePayment: "支付类型不支持线下支付"

CustomerIDNotFound: "会员ID不存在"
OwnerCannotBeEdit: "店主信息不能编辑"
EmployeeNoExists: "员工编号已存在"
EmployeeForbidden: "员工已禁用, 请联系管理员"
EmployeeNotFound: "商户员工不存在"

## 订单相关
OrderExists: "订单号已存在"
OrderNotFound: "订单不存在"
TimeRangeExceedsDays: "开始时间和结束时间不能超过%d天"
InvalidOrderDetailID: "点菜信息不存在"
OrderDetailAlreadyCancelled: "点菜信息已取消"
InvalidRefundPassword: "退款密码有误"
NoRefundPassword: "请设置退款密码"
InvalidOrderID: "订单号不存在"
InvalidOrderState: "订单状态有误"
FoodsCountTooMuch: "菜品退款数量超过下单数量"
OrderRefundSuccess: "订单退款成功"
OrderNotPaid: "订单未支付"
VipPaymentAmountError: "会员卡支付金额小于订单金额"
PaymentAmountInsufficient: "支付金额小于订单金额"
OrderStateNotAllowPay: "订单状态不允许支付"
OrderStateNotAllowCheckout: "订单状态不允许结算"
CancelQuantityExceed: "取消数量不能大于已点数量"
GetOrderNoFailed: "获取订单号失败"
OrderStateInvalid: "订单状态无效"
AddFoodFailed: "添加食品失败"
FoodNotFound: "未找到菜品"
GetOrderDetailsFailed: "获取订单详情失败"
FoodStateNotAllowCancel: "菜品状态不允许取消"
CustomerCountMissing: "客人数量缺失"
TableInfoMissing: "餐桌信息缺失"
CreateOrderFailed: "创建订单失败"
GetFoodCategoryFailed: "获取美食分类失败"
GetFoodListFailed: "获取美食列表失败"
CancelFoodFailed: "取消菜品失败"
OrderUpdateFailed: "更新订单失败"

# 交班
HasNoHandoverShift: "您有一条交班记录没有交班、无法开始开新班"
NoHandoverShift: "您没有有效的交班记录。请先开班后操作"
ShiftNotExist: "班次不存在"
ShiftNotOpen: "班次已禁用"

TableNotFound: "餐桌信息不存在"
TableNotChanged: "餐桌未发生变化"
ChangeTableFailed: "换台失败"
CurrentTableNotFound: "当前餐桌信息不存在"
TargetTableNotFound: "目标餐桌信息不存在"
OrderStateNotAllowChangeTable: "订单状态不允许换台"

QuerySubscribePermissionFailed: "查询订阅权限失败"
CreatedSubscribePermissionFailed: "创建订阅权限失败"
QueryPublishPermissionFailed: "查询客户端发布权限失败"
CreatePublishPermissionFailed: "创建客户端发布权限失败"
"permission:merchant-cashier-login": "收银机登录"
"permission:merchant-info-management": "商家信息管理"
"permission:area-management": "区域管理"
"permission:bill-management": "账单管理"
"permission:food-management": "美食管理"
"permission:vip-management": "会员管理"
"permission:debt-management": "赊账管理"
"permission:merchant-setting-management": "商户设置"
"permission:payment-type-management": "支付方式管理"
"permission:printer-management": "打印机管理"
"permission:remark-management": "备注管理"
"permission:waiter-service-management": "呼叫服务管理"
"permission:shift-management": "班次管理"
"permission:statistic-management": "统计"
"permission:table-management": "台桌管理"
"permission:user-management": "员工管理"
"permission:sms-management": "短信管理"
"permission:handover-management": "交班"
"permission:stock-management": "估清"
"permission:manage-table-qrcode": "台桌二维码管理"
"permission:order-open": "开台"
"permission:order-settle-offline": "订单线下结算"
"permission:order-settle-online": "订单线上结算"
"permission:order-cancel": "订单取消"
"permission:order-detail": "订单详情"
"permission:order-cancel-food": "订单取消菜品"
"permission:order-change-table": "订单换台"
"permission:order-collage": "订单拼桌"
"permission:order-split": "订单拆单"
"permission:order-urge": "催菜"
"table": "台桌"
"setting": "设置"

# 短信发送
"FailedToSendSMS": "发送短信失败"
"InvalidVerifyCode": "验证码错误"
"FailedToUpdatePassword": "更新密码失败"
"TooManySMSRequests": "发送短信过于频繁，请一个小时候重试。"

# 统计部分
StatTotalAmount: "交易额"
StatRealAmount: "实收金额"
StatCanceledAmount: "退菜金额"
StatRefundedAmount: "退款金额"
StatTotalCost: "总成本"
StatTotalDiscount: "会员优惠"
StatTotalProfit: "总利润"
StatIgnorePrice: "抹零价格"
StatGiveChange: "找零金额"
StatOrdersCount: "总单数"
StatOrderAvg: "单均消费"
StatCustomersAvg: "人均消费"
StatCustomerCount: "总客户数"

#星期几
Days:
  Monday: "周一"
  Tuesday: "周二"
  Wednesday: "周三"
  Thursday: "周四"
  Friday: "周五"
  Saturday: "周六"
  Sunday: "周日"
  Yesterday: "昨天"
  Last: "上"
  LastWeek: "上周"
  Day: "日"
  Day2: "日"
# 赊账管理
DebtHolderNotFound: "赊账人不存在"
PhoneExists: "手机号已存在"
DebtHolderCreateSuccess: "赊账人创建成功"
DebtHolderUpdateSuccess: "赊账人更新成功"
DebtHolderDeleteSuccess: "赊账人删除成功"
DebtHolderCreateFailed: "赊账人创建失败"
DebtHolderUpdateFailed: "赊账人更新失败"
DebtHolderDeleteFailed: "赊账人删除失败"

# 赊账交易记录
DebtTransactionNotFound: "交易记录不存在"
DebtHolderBalanceNotEnough: "赊账人余额不足"
InvalidID: "无效的ID参数"
DebtTransactionTypeDebt: "赊账"
DebtTransactionTypeRepayment: "还款"
DebtTransactionTypeRefund: "退菜"
DebtTransactionTypeTotal: "全部"

# 菜品分类
CategoryNameExists : "分类名称已存在"
FoodCategoryNotFound: "菜品分类不存在"
CategoryHasActiveFood: "分类下有的菜品数据，无法删除"
CategoryHasActiveAddition: "加料分组下有加料数据，无法删除"
CategoryHasActiveLunchBox: "分类下有餐盒数据，无法删除"

# 菜品相关
FoodNameExists: "菜品名称已存在"
ShortcutCodeExists: "快捷码已存在"
FoodComboInfoCantBeNull: "套餐信息不能为空"
FoodPriceAndComboPriceNotEqual: "菜品价格与套餐价格不匹配"
InvalidFoodID: "无效的菜品ID"
ThisFoodHaveUnpaidOrder: "该菜品有未支付的订单"
FoodInComboFoods: "该菜品已设置套餐，无法删除"
InvalidRequestType: "无效的请求类型"
FoodPriceInvalid: "菜品价格无效"
FoodVipPriceInvalid: "菜品会员价无效"
FoodCostPriceInvalid : "菜品成本价无效"

# 做法分组
MethodGroupNameExists: "做法分组名称已存在"
MethodGroupNotFound: "做法分组不存在"
MethodGroupHasMethods: "做法分组下有做法，无法删除"
SortSaveSuccess: "排序保存成功"

# 做法
MethodNameExists: "做法名称已存在"
MethodNotFound: "做法不存在"
MethodHasFoods: "做法有关联的美食，无法删除"
SaveSuccess: "保存成功"

# 规格相关
FoodSpecNameExists: "规格名称已存在"
FoodSpecNotFound: "规格不存在"
FoodSpecHasRelatedFoods: "规格有关联的菜品，无法删除"

# 菜品类型相关
FoodIsCombo: "菜品是套餐，无法操作"
FoodIsNotCombo: "菜品不是套餐，无法操作"
FoodIsSpec: "菜品是规格，无法操作"
FoodTypeNotFood: "菜品类型不是普通菜品"

# 加料相关
AdditionNameExists: "加料名称已存在"
AdditionCategoryNotFound: "加料分类不存在"

# 餐盒相关
LunchBoxNameExists: "餐盒名称已存在"
LunchBoxNotFound: "餐盒不存在"

# v3版本加菜相关错误
FoodSpecFoodNotFound: "规格对应的美食不存在"
MethodGroupMismatch: "做法分组不匹配"
FoodAdditionNotFound: "加料不存在"

# v3版本退菜相关错误
OrderDetailIDMismatch: "订单详情ID不匹配"
CannotCancelComboSubItem: "不能单独退套餐子项"
InvalidCancelMode: "无效的退菜模式"

# 系统错误
LanguagePackLoadFailed: "语言包读取失败"

Fields:
  UserMerchantsRequest:
    Username: 用户名
    Password: 密码

  LoginForm:
    Username: 用户名
    Password: 密码
    MerchantNo: 商户编号
    UUID: 设备识别码
    Ipv4: IPv4地址
    Ipv6: IPv6地址
    Confirm: 是否作为主收银台登录

  HandoverOpenForm:
    UserID: 当前用户
    MerchantNo: 商户号
    Shift: 班次
    WorkingBalance: 工作余额
    AlternateAmount: 代替金额

  HandoverForm:
    UserID: 当前用户
    MerchantNo: 商户号

  PrintRequest:
    DeviceID: 设备ID
    Content: 打印内容

  LocalServerForm:
    MerchantNo: 商家唯一编号
    UUID: 本地服务识别码
    Ipv4: IPv4地址
    Ipv6: IPv6地址
    Token: 授权码
    Status: 状态

  CreateEmployeeRequest:
    Phone: 手机号
    No: 编号
    NameUg: 维语名称
    NameZh: 中文名称
    State: 状态
    RoleIds: 角色ID列表

  SetOperationPasswordRequest:
    BatchID: 批次ID
    Code: 验证码
    Password: 密码
    ConfirmPassword: 确认密码

  UpdateEmployeeRequest:
    No: 编号
    NameUg: 维语名称
    NameZh: 中文名称
    State: 状态
    RoleIds: 角色列表

  UpdateStateRequest:
    State: 状态

  CreateRoleRequest:
    Name_zh: 中文名称
    Name_ug: 维语名称
    Permissions: 权限列表
    State: 状态

  UpdateRoleRequest:
    Name_zh: 中文名称
    Name_ug: 维语名称
    Permissions: 权限列表
    State: 状态

  UpdateRoleStateRequest:
    State: 状态

  RefundOrderForm:
    OrderID: 订单ID
    OrderDetails:
      ID: ID
      FoodsCount: 菜品数量
      Price: 价格
      Remarks: 备注
    Payments: 退款方式
    Password: 操作密码
    MerchantNo: 商户号
    CashierID: 收银员ID

  OrderSyncRequest:
    Order:
      No: 订单号
      MerchantNo: 商户号
      CustomerID: 会员ID
      TableID: 餐桌ID
      TerminalID: 终端ID
      CustomersCount: 顾客数
      FoodsCount: 菜品数
      CostPrice: 成本价
      OriginalPrice: 原价
      VipPrice: 会员价
      Price: 实际价格
      CollectedAmount: 已收金额
      GiveChange: 找零
      UserID: 用户ID
      CashierID: 收银员ID
      State: 订单状态
      Remarks: 备注
      TaxTicket: 是否开票
      IsPrint: 是否打印
      PaidAt: 支付时间
      CreatedAt: 创建时间
      UpdatedAt: 更新时间
    OrderDetails:
      MerchantNo: 商户号
      OrderID: 订单ID
      FoodID: 菜品ID
      FoodsCount: 菜品数量
      CostPrice: 成本价
      OriginalPrice: 原价
      VipPrice: 会员价
      Price: 实际价格
      TotalPrice: 总价
      IsPrint: 是否打印
      UserID: 用户ID
      State: 订单状态
      Remarks: 备注
      IsCombo: 是否套餐
      CreatedAt: 创建时间
      UpdatedAt: 更新时间
      ComboInfo:
        ID: 套餐detail编号
        FoodID: 美食编号
        Count: 美食数量
    OrderPayments:
      SyncOrderPaymentForm:
        MerchantNo: 商户编号
        OrderID: 订单ID
        OrderNo: 订单编号
        PaymentTypeID: 支付方式ID
        PaymentNo: 支付编号
        Amount: 支付金额
        PayType: 支付方式
        Remark: 备注信息
        State: 支付状态
        CashierID: 收银员ID
        PaidAt: 支付时间
        CreatedAt: 创建时间
        UpdatedAt: 更新时间

  MerchantPaymentForm:
    OrderNo: 订单编号
    PaymentNo: 支付信息编号
    Amount: 支付金额
    MerchantNo: 商家编号
    PaymentTypeID: 支付方式
    CustomerID: 会员ID
    CashierID: 收银员ID

  MicroPayForm:
    OrderNo: 订单编号
    PaymentNo: 支付信息编号
    Amount: 支付金额
    MerchantNo: 商家编号
    PaymentTypeID: 支付方式
    CustomerID: 会员ID
    CashierID: 收银员ID
    AuthCode: 付款码编号

  PaymentQueryForm:
    PaymentNo: 支付信息编号

  JsAPIPaymentForm:
    AuthCode: 付款码编号

  CustomerPaymentForm:
    OrderNo: 订单编号
    PaymentNo: 支付信息编号
    Amount: 支付金额
    MerchantNo: 商家编号
    PaymentTypeID: 支付方式
    AuthCode: 付款码编号
    CustomerID: 会员ID
    CashierID: 收银员ID
    Password: 支付密码

  PaymentRefundForm:
    OrderNo: 订单编号
    PaymentNo: 支付信息编号
    CashierID: 收银员ID
    MerchantNo: 商家编号

  PrinterSyncRequest:
    ID: 打印机ID
    MerchantNo: 商户号
    NameZh: 中文名称
    NameUg: 维语名称
    ConnectionType: 连接方式
    IpAddress: IP地址
    UsbPort: USB端口号
    Cloud: 云打印机号
    PaperWidth: 纸张宽度
    PrintMode: 打印方式
    Status: 打印机状态
    Buzzer: 蜂鸣器
    CreatedAt: 创建时间
    UpdatedAt: 更新时间
    DeletedAt: 删除时间
    CashierConfig: 收银打印机配置
    KitchenConfig: 后厨打印机配置

  RechargeRequest:
    CustomerID: 会员ID
    RechargeAmount: 充值金额
    PresentAmount: 赠送金额
    CashierID: 收银员ID
    MerchantNo: 商家编号
    PaymentTypeID: 支付方式

  RechargeMicroPayForm:
    RechargeRequest:
      CustomerID: 会员ID
      RechargeAmount: 充值金额
      PresentAmount: 赠送金额
      CashierID: 收银员ID
      MerchantNo: 商家编号
      PaymentTypeID: 支付方式
    AuthCode: 付款码编号

  RechargeOfflineForm:
    RechargeRequest:
      CustomerID: 会员ID
      RechargeAmount: 充值金额
      PresentAmount: 赠送金额
      CashierID: 收银员ID
      MerchantNo: 商家编号
      PaymentTypeID: 支付方式

  RpcInvokeRequest:
    Method: 方法
    Path: 路径
    ContentType: 内容类型
    Body: 请求体
    MerchantNo: 商户号
    Headers:
      Name: 请求头名称
      Value: 请求头值

  SendSmsForm:
    Phone: 手机号

  UserForm:
    MerchantNo: 商户号
    Name: 名称
    Password: 密码
    Phone: 手机号
    State: 状态

  VerifySmsForm:
    Phone: 手机号
    BatchID: 批次ID
    Code: 验证码
    Password: 密码

  ScanOrderCreateRequest:
    WechatUserID: 微信用户ID
    OpenID: 微信OpenID
    CustomersCount: 顾客数
    TableID: 餐桌ID
    Terminal: 终端
    PaymentID: 支付方式ID
    OrderDetailsName: 订单数据
    OrderDetails:
      FoodID: 美食信息
      FoodsCount: 菜品数量
      Remarks: 备注信息
      ComboInfo:
        ID: 套餐信息
        FoodID: 美食编号
        Count: 美食数量
    Remark: 订单备注
  FoodSellClearDataRequest:
    ID: 菜品ID
    CellClearState: 沽清状态
    SellClearCount: 剩余限量
    RemainingCount: 剩余数量

  PaymentRequest:
    OrderId: 订单ID
    OrderNo: 订单编号
    PaymentId: 支付方式ID
    PaymentNo: 支付编号
    Amount: 支付金额

  MicroPayRequest:
    OrderId: 订单ID
    OrderNo: 订单编号
    PaymentId: 支付方式ID
    PaymentNo: 支付编号
    Amount: 支付金额
    AuthCode: 付款码

  PaymentReverseRequest:
    OrderId: 订单ID
    OrderNo: 订单编号
    PaymentNo: 支付编号

  CustomerPayRequest:
    OrderId: 订单ID
    OrderNo: 订单编号
    PaymentNo: 支付编号
    Amount: 支付金额
    CustomerID: 会员ID
    Password: 支付密码

  CreateDebtHolderRequest:
    NameZh: 中文姓名
    NameUg: 维语姓名
    Phone: 手机号
    CreditLimit: 信用额度
    Status: 状态
  UpdateDebtHolderRequest:
    NameZh: 中文姓名
    NameUg: 维语姓名
    Phone: 手机号
    CreditLimit: 信用额度
    Status: 状态

  DebtTransactionRequest:
    HolderID: 赊账人ID
    Type: 交易类型
    BeginAt: 开始时间
    EndAt: 结束时间

  DebtRepaymentMicroPayForm:
    DebtRepaymentRequest:
      HolderID: 会员ID
      Amount: 支付金额
      CashierID: 收银员ID
      MerchantNo: 商家编号
      PaymentTypeID: 支付方式
    AuthCode: 付款码编号

  DebtRepaymentOfflineForm:
    DebtRepaymentRequest:
      HolderID: 会员ID
      Amount: 支付金额
      CashierID: 收银员ID
      MerchantNo: 商家编号
      PaymentTypeID: 支付方式

  CreateFoodCategoryRequest:
    NameUg: 维语名称
    NameZh: 中文名称
    Sort: 排序
    State: 状态
    Type: 分类类型

  UpdateFoodCategoryRequest:
    NameUg: 维语名称
    NameZh: 中文名称
    Sort: 排序
    State: 状态

  UpdateFoodCategoryStateRequest:
    State: 状态

  FoodCategoryListRequest:
    MerchantNo: 商户编号
    State: 状态

  CreateFoodRequest:
    FoodCategoryID: 菜品分类ID
    Image: 图片
    ShortcutCode: 快捷码
    NameUg: 维语名称
    NameZh: 中文名称
    CostPrice: 成本价
    VipPrice: 会员价
    Price: 现价
    FormatID: 规格ID
    IsSpecialFood: 是否特色菜
    SupportScanOrder: 是否支持扫码点单
    CellClearState: 是否设置剩余数量
    SellClearCount: 美食剩余限量数
    RemainingCount: 美食剩余数量
    IsCombo: 是否套餐菜
    Sort: 排序
    State: 状态
    ComboFoods: 套餐菜品列表

  UpdateFoodRequest:
    FoodCategoryID: 菜品分类ID
    Image: 图片
    ShortcutCode: 快捷码
    NameUg: 维语名称
    NameZh: 中文名称
    CostPrice: 成本价
    VipPrice: 会员价
    Price: 现价
    FormatID: 规格ID
    IsSpecialFood: 是否特色菜
    SupportScanOrder: 是否支持扫码点单
    CellClearState: 是否设置剩余数量
    SellClearCount: 美食剩余限量数
    RemainingCount: 美食剩余数量
    IsCombo: 是否套餐菜
    Sort: 排序
    State: 状态
    ComboFoods: 套餐菜品列表

  ComboFoodRequest:
    ComboFoodID: 套餐菜品ID
    ComboPrice: 套餐价格
    ComboCount: 套餐数量
    NameUg: 维语名称
    NameZh: 中文名称

  FoodListRequest:
    MerchantNo: 商户号
    FoodCategoryID: 分类ID
    Keyword: 关键词
    State: 状态
    SellClearAll: 是否清理所有销售
    SupportScanOrder: 是否支持扫码点单

  CreateFoodSpecRequest:
    NameZh: 规格名称(中文)
    NameUg: 规格名称(维语)

  UpdateFoodSpecRequest:
    NameZh: 规格名称(中文)
    NameUg: 规格名称(维语)

  FoodSpecListRequest:
    Keyword: 搜索关键词

  CreateMethodRequest:
    GroupID: 做法分组ID
    NameZh: 做法名称(中文)
    NameUg: 做法名称(维语)
    DescZh: 做法说明(中文)
    DescUg: 做法说明(维语)
    Price: 价格

  UpdateMethodRequest:
    GroupID: 做法分组ID
    NameZh: 做法名称(中文)
    NameUg: 做法名称(维语)
    DescZh: 做法说明(中文)
    DescUg: 做法说明(维语)
    Price: 价格

  MethodListRequest:
    GroupID: 做法分组ID
    Keyword: 搜索关键词

  CreateMethodGroupRequest:
    NameZh: 分组名称(中文)
    NameUg: 分组名称(维语)

  UpdateMethodGroupRequest:
    NameZh: 分组名称(中文)
    NameUg: 分组名称(维语)

  MethodGroupListRequest:
    Keyword: 搜索关键词

  CreateFoodAdditionRequest:
    FoodCategoryID: 加料分类ID
    Image: 图片
    ShortcutCode: 快捷码
    NameUg: 名称(维语)
    NameZh: 名称(中文)
    CostPrice: 成本价
    VipPrice: 会员价
    Price: 现价
    FormatID: 规格ID
    IsSpecialFood: 是否特色菜
    SupportScanOrder: 是否支持扫码点单
    Sort: 排序
    State: 状态

  UpdateFoodAdditionRequest:
    FoodCategoryID: 加料分类ID
    Image: 图片
    ShortcutCode: 快捷码
    NameUg: 名称(维语)
    NameZh: 名称(中文)
    CostPrice: 成本价
    VipPrice: 会员价
    Price: 现价
    FormatID: 规格ID
    IsSpecialFood: 是否特色菜
    SupportScanOrder: 是否支持扫码点单
    Sort: 排序
    State: 状态

  FoodAdditionListRequest:
    CategoryID: 加料分类ID
    Keyword: 搜索关键词
    State: 状态

  SaveFoodAdditionFoodsRequest:
    AdditionID: 加料ID
    Foods: 关联的菜品列表

  FoodAdditionAssociateItem:
    FoodID: 菜品ID
    Price: 加料价格

  RemoveFoodAdditionFoodsRequest:
    AdditionID: 加料ID
    FoodIDs: 菜品ID列表

  CreateLunchBoxRequest:
    Image: 图片
    ShortcutCode: 快捷码
    NameUg: 名称(维语)
    NameZh: 名称(中文)
    CostPrice: 成本价
    VipPrice: 会员价
    Price: 现价
    FormatID: 规格ID
    IsSpecialFood: 是否特色菜
    Sort: 排序
    State: 状态

  UpdateLunchBoxRequest:
    Image: 图片
    ShortcutCode: 快捷码
    NameUg: 名称(维语)
    NameZh: 名称(中文)
    CostPrice: 成本价
    VipPrice: 会员价
    Price: 现价
    FormatID: 规格ID
    IsSpecialFood: 是否特色菜
    SupportScanOrder: 是否支持扫码点单
    Sort: 排序
    State: 状态

  LunchBoxListRequest:
    Keyword: 搜索关键词
    State: 状态

  SaveLunchBoxSortRequest:
    LunchBoxes: 餐盒排序列表

  LunchBoxSortItem:
    ID: 餐盒ID
    Sort: 排序值

  SaveLunchBoxFoodsRequest:
    LunchBoxID: 餐盒ID
    Foods: 关联的菜品列表

  LunchBoxFoodItem:
    FoodID: 菜品ID
    Count: 餐盒数量

  RemoveLunchBoxFoodsRequest:
    LunchBoxID: 餐盒ID
    FoodIDs: 菜品ID列表

  FoodSpecSortItem:
    ID: 规格ID
    Sort: 排序值

  MethodGroupSortItem:
    ID: 分组ID
    Sort: 排序值

  FoodCategorySortItem:
    ID: 分类ID
    Sort: 排序值

  SaveFoodCategorySortRequest:
    Items: 排序项列表

  FoodSpecRequest:
    SpecId: 规格ID
    Price: 价格
    VipPrice: 会员价格
    CostPrice: 成本价
    LunchBoxes: 餐盒列表

  FoodLunchBoxRequest:
    LunchBoxID: 餐盒ID
    Count: 数量
# 通用message
Success: "مۇۋەپپىقىيەتلىك بولدى"
GetSuccess: "ئۇچۇر ئېلىندى"
GetFailed: "ئۇچۇر ئېلىش مەغلۇب بولدى"
CreateSuccess: "ئۇچۇر قوشۇلدى"
CreateFailed: "ئۇچۇر قوشۇش مەغلۇب بولدى"
UpdateSuccess: "ئۇچۇر يېڭىلاندى"
UpdateFailed: "ئۇچۇر يېڭىلاش مەغلۇب بولدى"
DeleteSuccess: "ئۇچۇر ئۆچۈرۈلدى"
DeleteFailed: "ئۇچۇر ئۆچۈرۈش مەغلۇب بولدى"
OperateSuccess: "مەشغۇلات مۇۋەپپىقىيەتلىك بولدى"
OperateFailed: "مەشغۇلات مەغلۇب بولدى"
DataNotFound: "ئۇچۇر مەۋجۇت ئەمەس"
NotFound: "مۇناسىۋەتلىك مەزمۇن تېپىلمىدى"
MethodNotAllowed: "يول قويۇلمىغان ئۇسۇل"
EmptyQueryResult: "تاللانغان شەرتكە ئۇيغۇن ئۇچۇر تېپىلمىدى"
ValidationFailed: "ئۇچۇر دەلىللەش مەغلۇب بولدى"
UserExists: "ئەزا  مەۋجۇت"
NoActiveMerchant: "ھېساباتتا ئاكتىپ دۇكان ئۇچۇرى تېپىلمىدى"
MerchantNotFound: "دۇكان ئۇچۇرى مەۋجۇت ئەمەس"
MerchantNotActive: "دۇكان ئاكتىپلانمىغان، باشقۇرغۇچى بىلەن ئالاقىلىشىڭ"
NoActiveMerchantFound: "ئاكتىپ دۇكان ئۇچۇرى تېپىلمىدى، باشقۇرغۇچى بىلەن ئالاقىلىشىڭ"
MerchantRoleNotFound: "تاللىغان رولىڭىز مەۋجۇت ئەمەس"
CustomerNotFound: "ئەزا ئۇچۇرى مەۋجۇت ئەمەس"
CustomerBalanceNotEnough: "ئەزالىق قالدۇق سوممىسى يېتەرلىك ئەمەس "
PasswordNotMatch: "مەخپىي نومۇر ماس كەلمىدى"
PasswordCannotBeSameAsOriginal: "يېڭى مەخپىي نومۇر ئەسلىدىكى بىلەن ئوخشاش بولسا بولمايدۇ"
OperationPasswordNotSet: "ئالدى بىلەن مەشغۇلات مەخپىي نومۇرىنى تەڭشەڭ"
InvalidOperationPassword: "مەشغۇلات مەخپىي نومۇرى خاتا"
InvalidClientType: "تېرمىنال تۈرى خاتا"

# 框架部分
FailedToParseJson: "ئۇچۇر توغرا ئەمەس: %v"
FailedToParseQuery: "ئۇچۇر توغرا ئەمەس: %v"
UnsupportedLocale: "قوللىمايدىغان تىل: %v"
InternalServerError: "مۇلازىمېتىردا خاتالىق كۆرۈلدى، سەل تۇرۇپ قايتا سىناڭ"

# 时间
InvalidDateRange: "ۋاقىت ئارلىقى توغرا ئەمەس"
InvalidBeginDateRange: "باشلانغان ۋاقىت فورماتى خاتا"
InvalidEndDateRange: "ئاخىرلاشقان ۋاقىت فورماتى خاتا"

# 业务部分
UserLoggedIn: "كىرىپ بولدىڭىز"
UserAlreadyLogin: "ئەزا باشقا ئاپاراتتا كىرىپ بولغان"
NoRightToLoginAsMaster: "ئاكونتىڭىز دۇكان باشقۇرغۇچى ئەمەس"
InvalidUsernameOrPassword: "ئەزا يانفون نومۇرى ياكى مەخپىي نومۇرى خاتا"
UserNotFound: "ئەزا ئۇچۇرى مەۋجۇت ئەمەس"
UserNotActive: "ئەزا ئاكتىپلانمىغان، باشقۇرغۇچى بىلەن ئالاقىلىشىڭ"
InvalidToken: "سىز تېخى كىرمەپسىز"
InvalidPhoneNumber: "يانفون نومۇرى توغرا ئەمەس"
NoRightToLoginCashier: "كەچۈرۈڭ، ئاپپاراتقا كىرىش ھوقۇقىڭىز يوقكەن"
PermissionDenied: "كەچۈرۈڭ، مەشغۇلات ھوقۇقىڭىز يوقكەن"
FailedToGetPermissions: "ھوقۇق ئېلىش مەغلۇب بولدى"
ConnectionFailedToLocalServer: "يەرلىك مۇلازىمىتىرغا ئۇلىنالمىدى"
FailedToGetLocaleService: "يەرلىك مۇلازىمىتىرنى ئوقۇش مەغلۇب بولدى"
FailedToRegisterLocalServer: "يەرلىك مۇلازىمىتىر تىزىملاش مەغلۇب بولدى"
FailedToInactiveLocalServer: "يەرلىك مۇلازىمىتىرنى تاقاش مەغلۇب بولدى"
FailedToCreateClientPublishPermission: "注册MQTT客户端发布权限失败"
HasActiveLocalServer: "باشقا يەرلىك مۇلازىمىتىر تىزىملانغان"
HasLoginAsMainServer: "ئەزا باش ئاپپارات سۈپىتىدا كىرىپ بولغان"
LocalServerNotFound: "يەرلىك مۇلازىمىتىر تېپىلمىدى"
NotEnoughDeviceInfo: "ئۈسكۈنە ئۇچۇرى تولۇق ئەمەس"
FailedToGetCloudAuthInfo: "بۇلۇت مۇلازىمېتىر ئۇچۇرىنى ئېلىش مەغلۇب بولدى"
FailedToGetHandoverLog: "ئىسمىنا ئۇچۇرىنى ئېلىش مەغلۇب بولدى"
HasNotSyncedOrders: "تورغا يوللانمىغان زاكازلار مەۋجۇتكەن، تورغا يوللانغاندىن كېيىن مەشغۇلات قىلىڭ"
FailedToGenerateToken: "生成Token失败"
LocalServerNotRegistered: "يەرلىك مۇلازىمىتىر تىزىملانمىغان، باش ئاپپاراتنى تەكشۈرۈڭ"
MerchantLocalServerNotActive: "ئاشخانا مۇلازىمېتىرى قوزغىتىلمىغان، ھازىرچە زاكاز چۈشۈرەلمەيسىز"
FailedToGetMerchantData: "ئاشخانا ئۇچۇرىنى ئېلىش مەغلۇب بولدى"
UserNotBindMerchantOrMerchantNotActive: "ئەزاغا باغلانغان ئاشخانا ئۇچۇرى يوق ياكى ئاشخانا توختىتىۋېتىلگەن"
MerchantNotSupportThirdPartPay: "سىز تېخى ئۈندىدار ياكى ئالىپاي پۇل تۆلەش ئىقتىدارىنى ئاچقۇزماپسىز، ساتقۇچى بىلەن ئالاقىلىشىڭ"
MerchantNotSupportWechatPay: "سىز تېخى ئۈندىدار پۇل تۆلەش ئىقتىدارىنى ئاچقۇزماپسىز، ساتقۇچى بىلەن ئالاقىلىشىڭ"
MerchantNotSupportAlipay: "سىز تېخى ئالىپاي پۇل تۆلەش ئىقتىدارىنى ئاچقۇزماپسىز، ساتقۇچى بىلەن ئالاقىلىشىڭ"
PleaseUseAlipay: "ئاشخانا ئۈندىدار پۇل تۆلەشنى قوللىمايدۇ، ئالىپاينى ئىشلىتىڭ"
PleaseUseWechatPay: "ئاشخانا ئالىپاي پۇل تۆلەشنى قوللىمايدۇ، ئۈندىدارنى ئىشلىتىڭ"
PaymentNoRequired: "پۇل تۆلەش كودى قۇرۇق بولسا بولمايدۇ"
PaymentExists: "پۇل تۆلەش كودى مەۋجۇتكەن"
PaymentNotFound: "پۇل تۆلەش كودى تېپىلمىدى"
PaymentNotPaid: "پۇل تۆلەش كودىغا پۇل تۆلەنمىگەن"
InvalidPayType: "پۇل تۆلەش شەكلى ئىناۋەتسىز"
PayAmountInvalid: "تۆلىگەن پۇل 0.01 يۈەندىن تۆۋەن بولسا بولمايدۇ"
AmountNotMatch: "تۆلىگەن پۇل زاكاز سوممىسى بىلەن بىردەك ئەمەس"
PaymentReverted: "پۇل قايتۇرۇلۇپ بولۇنغان"
PaymentExpired: "پۇل تۆلەش كودى ئىناۋەتسىز"
PaymentFailed: "پۇل تۆلەش مەغلۇب بولدى، سەل تۇرۇپ قايتا سىناڭ"
PaymentAlreadyPaid: "زاكازقا پۇل تۆلەنگەن"
PaymentListFailed: "پۇل تۆلەش ئۇچۇرىنى ئېلىش مەغلۇب بولدى"
ValidateSignFailed: "签名验证失败"
InvalidAuthCode: "توغرا بولغان ئۈندىدار ياكى ئالىپاي كودىنى كۆرسىتىڭ"
InvalidAlipayAuthCode: "توغرا بولغان ئالىپاي كودىنى كۆرسىتىڭ"
InvalidWechatAuthCode: "توغرا بولغان ئۈندىدار كودىنى كۆرسىتىڭ"
InvalidWechatCode: "微信code有误"
PaySuccess: "پۇل تۆلەندى"
PaymentQuerySuccess: "پۇل تۆلەش تەكشۈرۈلدى"
RefundSuccess: "پۇل قايتۇرۇلدى"
RefundFailed: "پۇل قايتۇرۇش مەغلۇب بولدى"
PaymentCannotRevert: "ھازىزقى ھالەت پۇل قايتۇرۇشنى قوللىمايدۇ"
RefundAmountExceed: "قايتۇرۇلغان پۇل تۆلىگەن پۇلدىن ئېشىپ كەتتى"
NotSupportOfflinePayment: "بۇ خىل پۇل تۆلەش ئۇسۇلىنى قوللىمايدۇ"

CustomerIDNotFound: "ئەزا ئۇچۇرى مەۋجۇت ئەمەس"
OwnerCannotBeEdit: "دۇكان ئېگىسى ئۈچۈرىنى تەھرىرلىيەلمەيسىز"
EmployeeNoExists: "خىزمەت نومۇرى ئىشلىتىلىپ بولغان"
EmployeeForbidden: "خىزمەتچى چەكلەنگەن، باشقۇرغۇچى بىلەن ئالاقىلىشىڭ"
EmployeeNotFound: "خىزمەتچى ئۇچۇرى مەۋجۇت ئەمەس"

## 订单相关
OrderExists: "زاكاز مەۋجۇتكەن"
OrderNotFound: "زاكاز مەۋجۇت ئەمەس"
TimeRangeExceedsDays: "باشلانغان ۋە ئاخىرلاشقان ۋاقىت %d كۈندىن ئېشىپ كەتتى"
InvalidOrderDetailID: "تاماق ئۇچۇرى مەۋجۇت ئەمەس"
OrderDetailAlreadyCancelled: "تاماق ئەمەلدىن قالدۇرۇلغان"
InvalidRefundPassword: "پۇل قايتۇرۇش مەخپىي نومۇرى خاتا"
NoRefundPassword: "پۇل قايتۇرۇش مەخپىي نومۇرىنى تەڭشەڭ"
InvalidOrderID: "زاكاز مەۋجۇت ئەمەس"
InvalidOrderState: "زاكاز ھالىتى توغرا ئەمەس"
FoodsCountTooMuch: "تاماقنىڭ قايتۇرغان سانى چۈشۈرگەن سانىدىن ئېشىپ كەتتى"
OrderRefundSuccess: "تاماق قايتۇرۇش مۇۋەپپىقىيەتلىك بولدى"
OrderNotPaid: "زاكازغا پۇل تۆلەنمىگەن"
VipPaymentAmountError: "ئەزالىق كارتا پۇل قايتۇرۇش سوممىسى زاكاز سوممىسىدىن تۆۋەن"
PaymentAmountInsufficient: "پۇل قايتۇرۇش سوممىسى زاكاز سوممىسىدىن تۆۋەن"
OrderStateNotAllowPay: "بۇ زاكاز ھالىتىدە پۇل تۆلىيەلمەيسىز"
OrderStateNotAllowCheckout: "بۇ زاكاز ھالىتىدە ھېساب بوغالمايسىز"
CancelQuantityExceed: "بىكار قىلىش سانى بۇيرۇتما سانىدىن ئېشىپ كەتتى"
GetOrderNoFailed: "بۇيرۇتما نومۇرىنى ئېلىش مەغلۇپ بولدى"
OrderStateInvalid: "زاكاز ھالىتى ئىناۋەتسىز"
AddFoodFailed: "تاماق قوشۇش مەغلۇپ بولدى"
FoodNotFound: "تاماق تېپىلمىدى"
GetOrderDetailsFailed: "زاكاز تەپسىلاتىنى ئېلىش مەغلۇپ بولدى"
FoodStateNotAllowCancel: "تاماق ھالىتى بىكار قىلىشقا يول قويمايدۇ"
CustomerCountMissing: "مېھمان سانى كەم"
TableInfoMissing: "ئۈستەل ئۇچۇرى كەم"
CreateOrderFailed: "زاكاز قۇرۇش مەغلۇپ بولدى"
GetFoodCategoryFailed: "تاماق تۈرىنى ئېلىش مەغلۇپ بولدى"
GetFoodListFailed: "تاماق تىزىملىكىنى ئېلىش مەغلۇپ بولدى"
CancelFoodFailed: "تاماق بىكار قىلىش مەغلۇپ بولدى"
OrderUpdateFailed: "زاكاز يېڭىلاش مەغلۇپ بولدى"

# 交班
HasNoHandoverShift: " سىزنىڭ تېخى تاپشۇرمىغان سىمىنېڭىز باركەن،يېڭى سىمىنا ئاچالمايسىز."
NoHandoverShift: "سىزنىڭ سىمىنا ئۇچۇرىڭىز يوقكەن، ئاۋۋال ئىشقا چىقىڭ"
ShiftNotExist: "سىمىنا مەۋجۇت ئەمەس"
ShiftNotOpen: "سىمىنا تاقىۋېتىلگەن"

TableNotFound: "ئۈستەل ئۇچۇرى تېپىلمىدى"
TableNotChanged: "ئۈستەل ئۇچۇرى ئۆزگەرمىدى"
ChangeTableFailed: "换台失败"
CurrentTableNotFound: "زاكاز ئۈستەل ئۇچۇرى تېپىلمىدى"
TargetTableNotFound: "يۆتكىلىدىغان ئۈستەل ئۇچۇرى تېپىلمىدى"
OrderStateNotAllowChangeTable: "زاكاز ھالىتى ئۈستەل يۆتكەشكە يول قويمايدۇ"

QuerySubscribePermissionFailed: "مۇشتەرى ھوقۇقىنى تەكشۈرۈش مەغلۇب بولدى"
CreatedSubscribePermissionFailed: "مۇشتەرى ھوقۇقىنى قۇرۇش مەغلۇب بولدى"
QueryPublishPermissionFailed: "خېرىدار تەرەپ ئېلان قىلىش ھوقۇقىنى تەكشۈرۈش مەغلۇب بولدى"
CreatePublishPermissionFailed: "خېرىدار تەرەپ ئېلان قىلىش ھوقۇقىنى قۇرۇش مەغلۇب بولدى"
"permission:merchant-cashier-login": "پۇل ئېلىش ئاپپاراتىغا كىرىش"
"permission:merchant-info-management": "دۇكان ئۇچۇرى باشقۇرۇش"
"permission:area-management": "رايون باشقۇرۇش"
"permission:bill-management": "ھېسابات باشقۇرۇش"
"permission:food-management": "تاماق باشقۇرۇش"
"permission:vip-management": "ئەزا باشقۇرۇش"
"permission:debt-management": "نىسى باشقۇرۇش"
"permission:merchant-setting-management": "ئاشخانا تەڭشىكى"
"permission:payment-type-management": "پۇل تۆلەش ئۇسۇلى باشقۇرۇش"
"permission:printer-management": "پىرىنتېر باشقۇرۇش"
"permission:remark-management": "ئىزاھات باشقۇرۇش"
"permission:waiter-service-management": "مۇلازىمەت باشقۇرۇش"
"permission:shift-management": "ئىسمېنا باشقۇرۇش"
"permission:statistic-management": "ئىستاتىستىكا"
"permission:table-management": "ئۈستەل باشقۇرۇش"
"permission:user-management": "خىزمەتچى باشقۇرۇش"
"permission:sms-management": "قىسقا ئۇچۇر باشقۇرۇش"
"permission:handover-management": "ئىسمېنا"
"permission:stock-management": "قالدۇق باشقۇرۇش"
"permission:manage-table-qrcode": "ئۈستەل ئىككىلىك كودى باشقۇرۇش"
"permission:order-open": "تاماق يېزىش"
"permission:order-settle-offline": "تورسىز ھېسابات قىلىش"
"permission:order-settle-online": "توردا ھېسابات قىلىش"
"permission:order-cancel": "زاكاز بىكار قىلىش"
"permission:order-detail": "زاكاز تەپسىلاتى"
"permission:order-cancel-food": "تاماق قالدۇرۇش"
"permission:order-change-table": "ئۈستەل يۆتكەش"
"permission:order-collage": "زاكاز بىرلەشتۈرۈش"
"permission:order-split": "زاكاز ئايرىش"
"permission:order-urge": "تاماق سۈيلەش"
"table": "ئۈستەل"
"setting": "تەڭشەك"

# 短信发送
"FailedToSendSMS": "ئۇچۇر يوللاش مەغلۇپ بولدى."
"InvalidVerifyCode": "تەستىق كودى توغرا ئەمەس"
"FailedToUpdatePassword": "مەخپىيي نومۇر ئۆزگەرتىش مەغلۇپ بولدى"
"TooManySMSRequests": "تەستىق كود يوللاپ قېتىم سانى چەكتىن ئېشىپ كەتتى، 1 سائەتتىن كېيىن قايتا سىناڭ"

# 统计部分
StatTotalAmount: "ئومۇمىي سودا"
StatRealAmount: "ئالغان پۇل"
StatCanceledAmount: "بىكار قىلىنغان"
StatRefundedAmount: "قايتۇرۇلغان پۇل"
StatTotalCost: "تەننەرخ"
StatTotalDiscount: "ئەزا ئېتىبارى"
StatTotalProfit: "پايدا"
StatIgnorePrice: "كىمەيتكەن باھا"
StatGiveChange: "ياندۇرغان پۇل"
StatOrdersCount: "زاكاز سانى"
StatOrderAvg: "ئوتتۇرچە زاكاز سوممىسى"
StatCustomersAvg: "ئوتتۇرچە ئىستېمال سوممىسى"
StatCustomerCount: "ئومۇمىي خېرىدار سانى"

#星期几
Days:
  Monday: "دۈشەنبە"
  Tuesday: "سەيشەنبە"
  Wednesday: "چارشەنبە"
  Thursday: "پەيشەنبە"
  Friday: "جۈمە"
  Saturday: "شەنبە"
  Sunday: "يەكشەنبە"
  Yesterday: "تۈنۈگۈن"
  Last: "ئالدىنقى "
  LastWeek: "ئالدىنقى ھەپتە"
  Day: "كۈنى"
  Day2: "كۈن"
# 赊账管理
DebtHolderNotFound: "ئەزا مەۋجۇت ئەمەس"
PhoneExists: "يانفون نومۇرى مەۋجۇت"
DebtHolderCreateSuccess: "ئەزا قوشۇلدى"
DebtHolderUpdateSuccess: "ئەزا يېڭىلاندى"
DebtHolderDeleteSuccess: "ئەزا ئۆچۈرۈلدى"
DebtHolderCreateFailed: "ئەزا قوشۇش مەغلۇب بولدى"
DebtHolderUpdateFailed: "ئەزا يېڭىلاش مەغلۇب بولدى"
DebtHolderDeleteFailed: "ئەزا ئۆچۈرۈش مەغلۇب بولدى"

# 赊账交易记录
DebtTransactionNotFound: "مۇئامىلە خاتىرىسى مەۋجۇت ئەمەس"
DebtHolderBalanceNotEnough: "ئىناۋەت سوممىسى يېتەرلىك ئەمەس"
InvalidID: "ئىناۋەتسىز ID پارامېتىرى"
DebtTransactionTypeDebt: "نىسى قىلغان"
DebtTransactionTypeRepayment: "نىسى قايتۇرغان"
DebtTransactionTypeRefund: "تاماق قايتۇرغان"
DebtTransactionTypeTotal: "بارلىق"

# 菜品分类
CategoryNameExists: "تاماق تۈرى ئۇچۇرى مەۋجۇت"
FoodCategoryNotFound: "تاماق تۈرى مەۋجۇت ئەمەس"
CategoryHasActiveFood: "تۈردە تاماق ئۇچۇرى بار، ئۆچۈرەلمەيسىز"
CategoryHasActiveAddition: "تۈردە ئاكتىپ تاماق ئۇچۇرى بار، ئۆچۈرەلمەيسىز"
CategoryHasActiveLunchBox: "تۈردە تاماق قاچا ئۇچۇرى بار، ئۆچۈرەلمەيسىز"

# 菜品相关
FoodNameExists: "تاماق ئىسمى مەۋجۇت"
ShortcutCodeExists: "تېزلەتمە نومۇر مەۋجۇت"
FoodComboInfoCantBeNull: "يۈرۈشلۈك ئۇچۇرى قۇرۇق بولسا بولمايدۇ"
FoodPriceAndComboPriceNotEqual: "تاماق باھاسى بىلەن يۈرۈشلۈك باھاسى ماس كەلمەيدۇ"
InvalidFoodID: "ئىناۋەتسىز تاماق نومۇرى"
ThisFoodHaveUnpaidOrder: "بۇ تاماقنىڭ پۇل تۆلەنمىگەن زاكىزى بار"
FoodInComboFoods: "بۇ تاماق يۈرۈشلۈك ئىچىدە بار، ئۆچۈرەلمەيسىز"
InvalidRequestType: "ئىناۋەتسىز تەلەپ تىپى"
FoodPriceInvalid: "تاماق باھاسى توغرا ئەمەس"
FoodVipPriceInvalid: "ئەزا باھاسى توغرا ئەمەس"
FoodCostPriceInvalid : "تاماق چىقىمى توغرا ئەمەس"

# 做法分组
MethodGroupNameExists: "خاسلىق گۇرۇپپا نامى مەۋجۇت"
MethodGroupNotFound: "خاسلىق گۇرۇپپا تېپىلمىدى"
MethodGroupHasMethods: "خاسلىق گۇرۇپپىسىدا ئۇسۇل بار، ئۆچۈرگىلى بولمايدۇ"
SortSaveSuccess: "تەرتىپ ساقلاندى"

# 做法
MethodNameExists: "خاسلىق نامى مەۋجۇت"
MethodNotFound: "خاسلىق ئۇچۇرى تېپىلمىدى"
MethodHasFoods: "خاسلىقنىڭ باغلانغان تاماقلىرى بار، ئۆچۈرگىلى بولمايدۇ"
SaveSuccess: "ساقلاندى"

# 规格相关
FoodSpecNameExists: "ئۆلچەم نامى مەۋجۇت"
FoodSpecNotFound: "ئۆلچەم تېپىلمىدى"
FoodSpecHasRelatedFoods: "ئۆلچەمنىڭ باغلانغان تاماقلىرى بار، ئۆچۈرگىلى بولمايدۇ"

# 菜品类型相关
FoodIsCombo: "يۈرۈشلۈك تاماق، مەشغۇلات قىلغىلى بولمايدۇ"
FoodIsNotCombo: "يۈرۈشلۈك تاماق ئەمەس، مەشغۇلات قىلغىلى بولمايدۇ"
FoodIsSpec: "تاماق ئۆلچەم بولسا بولمايدۇ"
FoodTypeNotFood: "تاماق تىپى ئادەتتىكى تاماق ئەمەس"

# 加料相关
AdditionNameExists: "قوشۇمچە نامى مەۋجۇت"
AdditionCategoryNotFound: "قوشۇمچە تۈرى تېپىلمىدى"

# 餐盒相关
LunchBoxNameExists: "تاماق قاچىسى نامى مەۋجۇت"
LunchBoxNotFound: "تاماق قاچىسى تېپىلمىدى"

# v3版本加菜相关错误
FoodSpecFoodNotFound: "ئۆلچەمگە ماس كېلىدىغان تاماق تېپىلمىدى"
MethodGroupMismatch: "ئۇسۇل گۇرۇپپىسى ماس كەلمىدى"
FoodAdditionNotFound: "قوشۇمچە تاماق تېپىلمىدى"

# v3版本退菜相关错误
OrderDetailIDMismatch: "بۇيرۇتما تەپسىلاتى ID ماس كەلمىدى"
CannotCancelComboSubItem: "يىغىندى تاماق تارماق تۈرىنى يەككە بىكار قىلغىلى بولمايدۇ"
InvalidCancelMode: "ئىناۋەتسىز بىكار قىلىش ئۇسۇلى"

# 系统错误
LanguagePackLoadFailed: "تىل بولىقى ئوقۇش مەغلۇب بولدى"

Fields:
  UserMerchantsRequest:
    Username: يانفۇن نومۇرى
    Password: مەخپىي نومۇر

  LoginForm:
    Username: يانفۇن نومۇرى
    Password: مەخپىي نومۇر
    MerchantNo: دۇكان نومۇرى
    UUID: ئۈسكۈنە كودى
    Ipv4: IPv4 ئادرېسى
    Ipv6: IPv6 ئادرېسى
    Confirm: باش ئاپپارات سۈپىتىدە كىرىش

  HandoverOpenForm:
    UserID: ئەزا نومۇرى
    MerchantNo: دۇكان نومۇرى
    Shift: ئىسمېنا
    WorkingBalance: خىزمەت قالدۇقى
    AlternateAmount: ئالماشتۇرۇش پۇلى

  HandoverForm:
    UserID: ئەزا نومۇرى
    MerchantNo: دۇكان نومۇرى

  PrintRequest:
    DeviceID: ئۈسكۈنە نومۇرى
    Content: بېسىش مەزمۇنى

  LocalServerForm:
    MerchantNo: دۇكان بىردىنبىر نومۇرى
    UUID: يەرلىك مۇلازىمەت كودى
    Ipv4: IPv4 ئادرېسى
    Ipv6: IPv6 ئادرېسى
    Token: ھوقۇق كودى
    Status: ھالەت

  CreateEmployeeRequest:
    Phone: تېلېفون نومۇرى
    No: نومۇر
    NameUg: ئۇيغۇرچە ئىسمى
    NameZh: خەنزۇچە ئىسمى
    State: ھالەت
    RoleIds: رول نومۇر تىزىملىكى

  SetOperationPasswordRequest:
    BatchID: تۈركۈم نومۇرى
    Code: تەستىق كودى
    Password: مەخپىي نومۇر
    ConfirmPassword: مەخپىي نومۇرنى جەزملەش

  UpdateEmployeeRequest:
    No: نومۇر
    NameUg: ئۇيغۇرچە ئىسمى
    NameZh: خەنزۇچە ئىسمى
    State: ھالەت
    RoleIds: رول تىزىملىكى

  UpdateStateRequest:
    State: ھالەت

  CreateRoleRequest:
    Name_zh: خەنزۇچە ئىسمى
    Name_ug: ئۇيغۇرچە ئىسمى
    Permissions: ھوقۇق تىزىملىكى
    State: ھالەت

  UpdateRoleRequest:
    Name_zh: خەنزۇچە ئىسمى
    Name_ug: ئۇيغۇرچە ئىسمى
    Permissions: ھوقۇق تىزىملىكى
    State: ھالەت

  UpdateRoleStateRequest:
    State: ھالەت

  RefundOrderForm:
    OrderID: زاكاز نومۇرى
    OrderDetails:
      ID: نومۇر
      FoodsCount: تاماق سانى
      Price: باھاسى
      Remarks: ئىزاھات
    Payments: قايتۇرۇش ئۇسۇلى
    Password: مەشغۇلات مەخپىي نومۇرى
    MerchantNo: دۇكان نومۇرى
    CashierID: پۇل ئالغۇچى

  OrderSyncRequest:
    Order:
      No: زاكاز نومۇرى
      MerchantNo: دۇكان نومۇرى
      CustomerID: ئەزا نومۇرى
      TableID: ئۈستەل نومۇرى
      TerminalID: تېرمىنال نومۇرى
      CustomersCount: مېھمان سانى
      FoodsCount: تاماق سانى
      CostPrice: تەننەرخ
      OriginalPrice: ئەسلى باھا
      VipPrice: ئەزا باھاسى
      Price: ئەمەلىي باھا
      CollectedAmount: ئالغان پۇل
      GiveChange: قايتۇرغان پۇل
      UserID: ئىشلەتكۈچى نومۇرى
      CashierID: پۇل ئالغۇچى
      State: زاكاز ھالىتى
      Remarks: ئىزاھات
      TaxTicket: تالۇن كېسىش
      IsPrint: بېسىش
      PaidAt: تۆلىگەن ۋاقتى
      CreatedAt: قۇرۇلغان ۋاقتى
      UpdatedAt: يېڭىلانغان ۋاقتى
    OrderDetails:
      MerchantNo: دۇكان نومۇرى
      OrderID: زاكاز نومۇرى
      FoodID: تاماق نومۇرى
      FoodsCount: تاماق سانى
      CostPrice: تەننەرخ
      OriginalPrice: ئەسلى باھا
      VipPrice: ئەزا باھاسى
      Price: ئەمەلىي باھا
      TotalPrice: جەمئىي باھا
      IsPrint: بېسىش
      UserID: ئىشلەتكۈچى نومۇرى
      State: زاكاز ھالىتى
      Remarks: ئىزاھات
      IsCombo: يۈرۈشلۈك
      CreatedAt: قۇرۇلغان ۋاقتى
      UpdatedAt: يېڭىلانغان ۋاقتى
      ComboInfo:
        ID: يۈرۈشلۈك نومۇرى
        FoodID: تاماق نومۇرى
        Count: تاماق سانى
    OrderPayments:
      SyncOrderPaymentForm:
        MerchantNo: دۇكان نومۇرى
        OrderID: زاكاز نومۇرى
        OrderNo: زاكاز نومۇرى
        PaymentTypeID: تۆلەش ئۇسۇلى نومۇرى
        PaymentNo: تۆلەش نومۇرى
        Amount: تۆلەش پۇلى
        PayType: تۆلەش ئۇسۇلى
        Remark: ئىزاھات
        State: تۆلەش ھالىتى
        CashierID: پۇل ئالغۇچى
        PaidAt: تۆلىگەن ۋاقتى
        CreatedAt: قۇرۇلغان ۋاقتى
        UpdatedAt: يېڭىلانغان ۋاقتى

  MerchantPaymentForm:
    OrderNo: زاكاز نومۇرى
    PaymentNo: تۆلەش نومۇرى
    Amount: تۆلەش پۇلى
    MerchantNo: دۇكان نومۇرى
    PaymentTypeID: تۆلەش ئۇسۇلى
    CustomerID: ئەزا نومۇرى
    CashierID: پۇل ئالغۇچى

  MicroPayForm:
    OrderNo: زاكاز نومۇرى
    PaymentNo: تۆلەش نومۇرى
    Amount: تۆلەش پۇلى
    MerchantNo: دۇكان نومۇرى
    PaymentTypeID: تۆلەش ئۇسۇلى
    CustomerID: ئەزا نومۇرى
    CashierID: پۇل ئالغۇچى
    AuthCode: تۆلەش كودى

  PaymentQueryForm:
    PaymentNo: تۆلەش نومۇرى

  JsAPIPaymentForm:
    AuthCode: تۆلەش كودى

  CustomerPaymentForm:
    OrderNo: زاكاز نومۇرى
    PaymentNo: تۆلەش نومۇرى
    Amount: تۆلەش پۇلى
    MerchantNo: دۇكان نومۇرى
    PaymentTypeID: تۆلەش ئۇسۇلى
    AuthCode: تۆلەش كودى
    CustomerID: ئەزا نومۇرى
    CashierID: پۇل ئالغۇچى
    Password: تۆلەش مەخپىي نومۇرى

  PaymentRefundForm:
    OrderNo: زاكاز نومۇرى
    PaymentNo: تۆلەش نومۇرى
    CashierID: پۇل ئالغۇچى
    MerchantNo: دۇكان نومۇرى

  PrinterSyncRequest:
    ID: پىرىنتېر نومۇرى
    MerchantNo: دۇكان نومۇرى
    NameZh: خەنزۇچە ئىسمى
    NameUg: ئۇيغۇرچە ئىسمى
    ConnectionType: ئۇلىنىش ئۇسۇلى
    IpAddress: IP ئادرېسى
    UsbPort: USB پورت نومۇرى
    Cloud: بۇلۇت پىرىنتېر نومۇرى
    PaperWidth: قەغەز كەڭلىكى
    PrintMode: بېسىش ئۇسۇلى
    Status: پىرىنتېر ھالىتى
    Buzzer: ئاۋاز چىقارغۇچ
    CreatedAt: قۇرۇلغان ۋاقتى
    UpdatedAt: يېڭىلانغان ۋاقتى
    DeletedAt: ئۆچۈرۈلگەن ۋاقتى
    CashierConfig: ئاپپارات پىرىنتېر تەڭشىكى
    KitchenConfig: ئاشخانا پىرىنتېر تەڭشىكى

  RechargeRequest:
    CustomerID: ئەزا نومۇرى
    RechargeAmount: پۇل قاچىلاش سوممىسى
    PresentAmount: سوۋغا پۇلى
    CashierID: پۇل ئالغۇچى
    MerchantNo: دۇكان نومۇرى
    PaymentTypeID: تۆلەش ئۇسۇلى

  RechargeMicroPayForm:
    RechargeRequest:
      CustomerID: ئەزا نومۇرى
      RechargeAmount: پۇل قاچىلاش سوممىسى
      PresentAmount: سوۋغا پۇلى
      CashierID: پۇل ئالغۇچى
      MerchantNo: دۇكان نومۇرى
      PaymentTypeID: تۆلەش ئۇسۇلى
    AuthCode: تۆلەش كودى

  RechargeOfflineForm:
    RechargeRequest:
      CustomerID: ئەزا نومۇرى
      RechargeAmount: پۇل قاچىلاش سوممىسى
      PresentAmount: سوۋغا پۇلى
      CashierID: پۇل ئالغۇچى
      MerchantNo: دۇكان نومۇرى
      PaymentTypeID: تۆلەش ئۇسۇلى

  RpcInvokeRequest:
    Method: ئۇسۇل
    Path: يول
    ContentType: مەزمۇن تىپى
    Body: مەزمۇن
    MerchantNo: دۇكان نومۇرى
    Headers:
      Name: تېما ئىسمى
      Value: تېما قىممىتى

  SendSmsForm:
    Phone: تېلېفون نومۇرى

  UserForm:
    MerchantNo: دۇكان نومۇرى
    Name: ئىسمى
    Password: مەخپىي نومۇر
    Phone: تېلېفون نومۇرى
    State: ھالەت

  VerifySmsForm:
    Phone: تېلېفون نومۇرى
    BatchID: تۈركۈم نومۇرى
    Code: تەستىق كودى
    Password: مەخپىي نومۇر

  ScanOrderCreateRequest:
    WechatUserID: ئەزا ئۇچۇرى
    OpenID: OpenID
    CustomersCount: مېھمان سانى
    TableID: ئۈستەل نومۇرى
    Terminal: تېرمىنال
    PaymentID: پۇل تۆلەش ئۇسۇلى
    OrderDetailsName: زاكاز مەزمۇنى
    OrderDetails:
      FoodID: تاماق ئۇچۇرى
      FoodsCount: تاماق سانى
      Remarks: ئىزاھات
      ComboInfo:
        ID: يۈرۈشلۈك ئۇچۇرى
        FoodID: تاماق نومۇرى
        Count: تاماق سانى
    Remark: زاكاز ئىزاھاتى
  FoodSellClearDataRequest:
    ID: تاماق نومۇرى
    CellClearState: قالدۇق ھالىتى
    SellClearCount: قالغان چەكلىمە سانى
    RemainingCount: قالغان تاماق سانى

  PaymentRequest:
    OrderId: زاكاز نومۇرى
    OrderNo: زاكاز نومۇرى
    PaymentId: تۆلەش ئۇسۇلى نومۇرى
    PaymentNo: تۆلەش نومۇرى
    Amount: تۆلەش پۇلى

  MicroPayRequest:
    OrderId: زاكاز نومۇرى
    OrderNo: زاكاز نومۇرى
    PaymentId: تۆلەش ئۇسۇلى نومۇرى
    PaymentNo: تۆلەش نومۇرى
    Amount: تۆلەش پۇلى
    AuthCode: تۆلەش كودى

  PaymentReverseRequest:
    OrderId: زاكاز نومۇرى
    OrderNo: زاكاز نومۇرى
    PaymentNo: تۆلەش نومۇرى

  CustomerPayRequest:
    OrderId: زاكاز نومۇرى
    OrderNo: زاكاز نومۇرى
    PaymentNo: تۆلەش نومۇرى
    Amount: تۆلەش پۇلى
    CustomerID: ئەزا نومۇرى
    Password: مەخپىي نومۇر

  CreateDebtHolderRequest:
    NameZh: خەنزۇچە ئىسىم
    NameUg: ئۇيغۇرچە ئىسىم
    Phone: تېلېفون نومۇرى
    CreditLimit: ئىناۋەت سوممىسى
    State: ھالىتى
  UpdateDebtHolderRequest:
    NameZh: خەنزۇچە ئىسىم
    NameUg: ئۇيغۇرچە ئىسىم
    Phone: تېلېفون نومۇرى
    CreditLimit: ئىناۋەت سوممىسى
    State: ھالىتى

  DebtTransactionRequest:
    HolderID: نىسى قىلغۇچى
    Type: مۇئامىلە تىپى
    BeginAt: باشلانغان ۋاقىت
    EndAt: ئاخىرلاشقان ۋاقىت

  DebtRepaymentMicroPayForm:
    DebtRepaymentRequest:
      HolderID: ئەزا نومۇرى
      Amount: قايتۇرغان پۇل
      CashierID: پۇل ئالغۇچى
      MerchantNo: دۇكان نومۇرى
      PaymentTypeID: تۆلەش ئۇسۇلى
    AuthCode: تۆلەش كودى

  DebtRepaymentOfflineForm:
    DebtRepaymentRequest:
      HolderID: ئەزا نومۇرى
      Amount: قايتۇرغان پۇل
      CashierID: پۇل ئالغۇچى
      MerchantNo: دۇكان نومۇرى
      PaymentTypeID: تۆلەش ئۇسۇلى

  CreateFoodCategoryRequest:
    NameUg: ئۇيغۇرچە ئىسمى
    NameZh: خەنزۇچە ئىسمى
    Sort: تەرتىپ
    State: ھالەت
    Type: كاتېگورىيە تۈرى

  UpdateFoodCategoryRequest:
    NameUg: ئۇيغۇرچە ئىسمى
    NameZh: خەنزۇچە ئىسمى
    Sort: تەرتىپ
    State: ھالەت

  UpdateFoodCategoryStateRequest:
    State: ھالەت

  FoodCategoryListRequest:
    MerchantNo: دۇكان نومۇرى
    State: ھالەت

  CreateFoodRequest:
    FoodCategoryID: تاماق تۈرى نومۇرى
    Image: رەسىم
    ShortcutCode: تېزلەتمە نومۇر
    NameUg: ئۇيغۇرچە ئىسمى
    NameZh: خەنزۇچە ئىسمى
    CostPrice: تەننەرخ
    VipPrice: ئەزا باھاسى
    Price: ھازىرقى باھا
    FormatID: ئۆلچەم نومۇرى
    IsSpecialFood: ئالاھىدە تاماقمۇ
    SupportScanOrder: سكاننېرلىق زاكازنى قوللاش
    CellClearState: قالدۇق ساننى بەلگىلەيدۇمۇ
    SellClearCount: تاماق قالدۇق چەكلىك سانى
    RemainingCount: تاماق قالدۇق سانى
    IsCombo: يۈرۈشلۈك تاماقمۇ
    Sort: تەرتىپ
    State: ھالەت
    ComboFoods: يۈرۈشلۈك تاماق تىزىملىكى

  UpdateFoodRequest:
    FoodCategoryID: تاماق تۈرى نومۇرى
    Image: رەسىم
    ShortcutCode: تېزلەتمە نومۇر
    NameUg: ئۇيغۇرچە ئىسمى
    NameZh: خەنزۇچە ئىسمى
    CostPrice: تەننەرخ
    VipPrice: ئەزا باھاسى
    Price: ھازىرقى باھا
    FormatID: ئۆلچەم نومۇرى
    IsSpecialFood: ئالاھىدە تاماقمۇ
    SupportScanOrder: سكاننېرلىق زاكازنى قوللاش
    CellClearState: قالدۇق ساننى بەلگىلەيدۇمۇ
    SellClearCount: تاماق قالدۇق چەكلىك سانى
    RemainingCount: تاماق قالدۇق سانى
    IsCombo: يۈرۈشلۈك تاماقمۇ
    Sort: تەرتىپ
    State: ھالەت
    ComboFoods: يۈرۈشلۈك تاماق تىزىملىكى

  ComboFoodRequest:
    ComboFoodID: يۈرۈشلۈك تاماق نومۇرى
    ComboPrice: يۈرۈشلۈك باھاسى
    ComboCount: يۈرۈشلۈك سانى
    NameUg: ئۇيغۇرچە ئىسمى
    NameZh: خەنزۇچە ئىسمى

  FoodListRequest:
    MerchantNo: دۇكان نومۇرى
    FoodCategoryID: تۈر نومۇرى
    Keyword: ئاچقۇچلۇق سۆز
    State: ھالەت
    SellClearAll: بارلىق ساتىشنى تازىلايدۇمۇ
    SupportScanOrder: سكاننېرلىق زاكازنى قوللاش

  CreateFoodSpecRequest:
    NameZh: ئۆلچەم نامى(خەنزۇچە)
    NameUg: ئۆلچەم نامى(ئۇيغۇرچە)

  UpdateFoodSpecRequest:
    NameZh: ئۆلچەم نامى(خەنزۇچە)
    NameUg: ئۆلچەم نامى(ئۇيغۇرچە)

  FoodSpecListRequest:
    Keyword: ئىزدەش ئاچقۇچلۇق سۆزى

  CreateMethodRequest:
    GroupID: خاسلىق گۇرۇپپا نومۇرى
    NameZh: خاسلىق نامى(خەنزۇچە)
    NameUg: خاسلىق نامى(ئۇيغۇرچە)
    DescZh: خاسلىق چۈشەندۈرۈلىشى(خەنزۇچە)
    DescUg: خاسلىق چۈشەندۈرۈلىشى(ئۇيغۇرچە)
    Price: باھاسى

  UpdateMethodRequest:
    GroupID: خاسلىق گۇرۇپپا نومۇرى
    NameZh: خاسلىق نامى(خەنزۇچە)
    NameUg: خاسلىق نامى(ئۇيغۇرچە)
    DescZh: خاسلىق چۈشەندۈرۈلىشى(خەنزۇچە)
    DescUg: خاسلىق چۈشەندۈرۈلىشى(ئۇيغۇرچە)
    Price: باھاسى

  MethodListRequest:
    GroupID: خاسلىق گۇرۇپپا نومۇرى
    Keyword: ئىزدەش ئاچقۇچلۇق سۆزى

  CreateMethodGroupRequest:
    NameZh: گۇرۇپپا نامى(خەنزۇچە)
    NameUg: گۇرۇپپا نامى(ئۇيغۇرچە)

  UpdateMethodGroupRequest:
    NameZh: گۇرۇپپا نامى(خەنزۇچە)
    NameUg: گۇرۇپپا نامى(ئۇيغۇرچە)

  MethodGroupListRequest:
    Keyword: ئىزدەش ئاچقۇچلۇق سۆزى

  CreateFoodAdditionRequest:
    FoodCategoryID: قوشۇمچە تۈر نومۇرى
    Image: رەسىم
    ShortcutCode: تېزلەتمە كودى
    NameUg: نامى(ئۇيغۇرچە)
    NameZh: نامى(خەنزۇچە)
    CostPrice: ئەسلى باھاسى
    VipPrice: ئەزا باھاسى
    Price: ھازىرقى باھاسى
    FormatID: ئۆلچەم نومۇرى
    IsSpecialFood: ئالاھىدە تاماقمۇ
    SupportScanOrder: سكاننېرلىق زاكازنى قوللاش
    Sort: تەرتىپ
    State: ھالەت

  UpdateFoodAdditionRequest:
    FoodCategoryID: قوشۇمچە تۈر نومۇرى
    Image: رەسىم
    ShortcutCode: تېزلەتمە كودى
    NameUg: نامى(ئۇيغۇرچە)
    NameZh: نامى(خەنزۇچە)
    CostPrice: ئەسلى باھاسى
    VipPrice: ئەزا باھاسى
    Price: ھازىرقى باھاسى
    FormatID: ئۆلچەم نومۇرى
    IsSpecialFood: ئالاھىدە تاماقمۇ
    SupportScanOrder: سكاننېرلىق زاكازنى قوللاش
    Sort: تەرتىپ
    State: ھالەت

  FoodAdditionListRequest:
    CategoryID: قوشۇمچە تۈر نومۇرى
    Keyword: ئىزدەش ئاچقۇچلۇق سۆزى
    State: ھالەت

  SaveFoodAdditionFoodsRequest:
    AdditionID: قوشۇمچە نومۇرى
    Foods: مۇناسىۋەتلىك تاماق تىزىملىكى

  FoodAdditionAssociateItem:
    FoodID: تاماق نومۇرى
    Price: قوشۇمچە باھاسى

  RemoveFoodAdditionFoodsRequest:
    AdditionID: قوشۇمچە نومۇرى
    FoodIDs: تاماق نومۇر تىزىملىكى

  CreateLunchBoxRequest:
    Image: رەسىم
    ShortcutCode: تېزلەتمە كودى
    NameUg: نامى(ئۇيغۇرچە)
    NameZh: نامى(خەنزۇچە)
    CostPrice: ئەسلى باھاسى
    VipPrice: ئەزا باھاسى
    Price: ھازىرقى باھاسى
    FormatID: ئۆلچەم نومۇرى
    IsSpecialFood: ئالاھىدە تاماقمۇ
    Sort: تەرتىپ
    State: ھالەت

  UpdateLunchBoxRequest:
    Image: رەسىم
    ShortcutCode: تېزلەتمە كودى
    NameUg: نامى(ئۇيغۇرچە)
    NameZh: نامى(خەنزۇچە)
    CostPrice: ئەسلى باھاسى
    VipPrice: ئەزا باھاسى
    Price: ھازىرقى باھاسى
    FormatID: ئۆلچەم نومۇرى
    IsSpecialFood: ئالاھىدە تاماقمۇ
    SupportScanOrder: سكاننېرلىق زاكازنى قوللاش
    Sort: تەرتىپ
    State: ھالەت

  LunchBoxListRequest:
    Keyword: ئىزدەش ئاچقۇچلۇق سۆزى
    State: ھالەت

  SaveLunchBoxSortRequest:
    LunchBoxes: تاماق قاچىسى تەرتىپ تىزىملىكى

  LunchBoxSortItem:
    ID: تاماق قاچىسى نومۇرى
    Sort: تەرتىپ قىممىتى

  SaveLunchBoxFoodsRequest:
    LunchBoxID: تاماق قاچىسى نومۇرى
    Foods: مۇناسىۋەتلىك تاماق تىزىملىكى

  LunchBoxFoodItem:
    FoodID: تاماق نومۇرى
    Count: تاماق قاچىسى سانى

  RemoveLunchBoxFoodsRequest:
    LunchBoxID: تاماق قاچىسى نومۇرى
    FoodIDs: تاماق نومۇر تىزىملىكى

  FoodSpecSortItem:
    ID: ئۆلچەم نومۇرى
    Sort: تەرتىپ قىممىتى

  MethodGroupSortItem:
    ID: گۇرۇپپا نومۇرى
    Sort: تەرتىپ قىممىتى

  FoodCategorySortItem:
    ID: تۈر نومۇرى
    Sort: تەرتىپ قىممىتى

  SaveFoodCategorySortRequest:
    Items: تەرتىپ تۈر تىزىملىكى

  FoodSpecRequest:
    SpecId: ئۆلچەم نومۇرى
    Price: باھاسى
    VipPrice: ئەزا باھاسى
    CostPrice: ئەسلى باھاسى
    LunchBoxes: تاماق قاچىسى تىزىملىكى

  FoodLunchBoxRequest:
    LunchBoxID: تاماق قاچىسى نومۇرى
    Count: سانى
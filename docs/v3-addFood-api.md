# V3版本加菜接口文档

## 概述

v3版本的加菜接口在v2基础上扩展了对美食规格、做法、加料和餐盒的完整支持。

## 接口地址

- **v2兼容接口**: `POST /api/v3/orders/add-foods/:id`
- **v3扩展接口**: `POST /api/v3/orders/add-foods-v3/:id`

## 主要变化

### 1. 数据结构变化

#### v2版本 (OrderDetail)
```json
{
  "food_id": 123,
  "foods_count": 2.0,
  "remarks": "备注",
  "combo_info": null
}
```

#### v3版本 (OrderDetailV3)
```json
{
  "food_id": 123,        // 美食ID（父级菜品ID）
  "spec_food_id": 456,   // 规格ID（如果没有规格，则与food_id相同）
  "foods_count": 2.0,
  "remarks": "备注",
  "combo_info": null,
  "additions": [],       // 加料（作为子级）
  "methods": [],         // 做法（作为子级）
  "lunch_boxes": []      // 餐盒（作为子级）
}
```

### 2. 订单详情存储结构

v3版本采用父子级关系存储，使用Pid字段建立关联：

1. **规格信息作为父级**：存储在order_details表中，type=1，pid=0（或父级ID）
2. **加料作为子级**：存储在order_details表中，type=2，pid=父级订单详情ID
3. **餐盒作为子级**：存储在order_details表中，type=3，pid=父级订单详情ID
4. **做法作为子级**：存储在order_details表中，type=4，pid=父级订单详情ID

#### Pid字段说明
- **Pid=0**：表示顶级订单详情（主菜品/规格）
- **Pid>0**：表示子级订单详情，Pid值为其父级订单详情的ID
- 支持多层级关系：子级的子级也可以有自己的子级

### 3. 规格处理逻辑

- 菜品和规格共用foods表，使用pid字段进行绑定
- 如果没有规格，将菜品自己看成默认规格（spec_food_id = food_id）
- 如果有规格，spec_food_id为具体的规格ID

## 请求示例

### 基础菜品（无规格）
```json
{
  "order_details": [
    {
      "food_id": 123,
      "spec_food_id": 123,  // 与food_id相同，表示无规格
      "foods_count": 1.0,
      "remarks": "不要辣"
    }
  ],
  "order_remark": "订单备注"
}
```

### 带规格的菜品
```json
{
  "order_details": [
    {
      "food_id": 123,      // 父级菜品ID
      "spec_food_id": 456, // 规格ID
      "foods_count": 1.0,
      "remarks": "中辣"
    }
  ],
  "order_remark": "订单备注"
}
```

### 带加料的菜品
```json
{
  "order_details": [
    {
      "food_id": 123,
      "spec_food_id": 456,
      "foods_count": 1.0,
      "remarks": "主菜",
      "additions": [
        {
          "food_id": 789,    // 加料ID
          "spec_food_id": 789,
          "foods_count": 1.0,
          "remarks": "加料备注"
        }
      ]
    }
  ],
  "order_remark": "订单备注"
}
```

### 带做法的菜品
```json
{
  "order_details": [
    {
      "food_id": 123,
      "spec_food_id": 456,
      "foods_count": 1.0,
      "remarks": "主菜",
      "methods": [
        {
          "method_id": 101,
          "group_id": 201,
          "count": 1
        }
      ]
    }
  ],
  "order_remark": "订单备注"
}
```

### 带餐盒的菜品
```json
{
  "order_details": [
    {
      "food_id": 123,
      "spec_food_id": 456,
      "foods_count": 1.0,
      "remarks": "主菜",
      "lunch_boxes": [
        {
          "food_id": 301,    // 餐盒ID
          "spec_food_id": 301,
          "foods_count": 1.0,
          "remarks": "餐盒备注"
        }
      ]
    }
  ],
  "order_remark": "订单备注"
}
```

### 复合示例（包含所有类型）
```json
{
  "order_details": [
    {
      "food_id": 123,
      "spec_food_id": 456,
      "foods_count": 2.0,
      "remarks": "主菜，中辣",
      "additions": [
        {
          "food_id": 789,
          "spec_food_id": 789,
          "foods_count": 1.0,
          "remarks": "加鸡蛋"
        }
      ],
      "methods": [
        {
          "method_id": 101,
          "group_id": 201,
          "count": 1
        }
      ],
      "lunch_boxes": [
        {
          "food_id": 301,
          "spec_food_id": 301,
          "foods_count": 2.0,
          "remarks": "大号餐盒"
        }
      ]
    }
  ],
  "order_remark": "整个订单的备注"
}
```

## 套餐处理

套餐的处理方式与普通菜品类似：
- 套餐作为父级（is_combo=true）
- 如果套餐有加料/做法/餐盒，作为子级保存

## 向后兼容性

- v2接口（`/api/v3/orders/add-foods/:id`）继续支持原有的OrderAddFoodRequest结构
- v3接口（`/api/v3/orders/add-foods-v3/:id`）使用新的OrderAddFoodRequestV3结构
- 现有v2客户端无需修改即可继续使用

## 数据库存储示例

假设有以下请求：
```json
{
  "order_details": [
    {
      "food_id": 123,
      "spec_food_id": 456,
      "foods_count": 1.0,
      "additions": [
        {
          "food_id": 789,
          "spec_food_id": 789,
          "foods_count": 1.0
        }
      ],
      "methods": [
        {
          "method_id": 101,
          "group_id": 201,
          "count": 1
        }
      ]
    }
  ]
}
```

在order_details表中的存储结构：
```
ID  | Pid | FoodID | SpecFoodID | MethodID | Type | 说明
----|-----|--------|------------|----------|------|--------
100 | 0   | 123    | 456        | NULL     | 1    | 主菜品（规格）
101 | 100 | 789    | 789        | NULL     | 2    | 加料（父级ID=100）
102 | 100 | NULL   | NULL       | 101      | 4    | 做法（父级ID=100）
```

## 错误处理

新增的错误码：
- `FoodSpecNotFound`: 美食规格不存在
- `FoodSpecFoodNotFound`: 规格对应的美食不存在
- `MethodNotFound`: 做法不存在
- `MethodGroupMismatch`: 做法分组不匹配
- `FoodAdditionNotFound`: 加料不存在
- `LunchBoxNotFound`: 餐盒不存在
